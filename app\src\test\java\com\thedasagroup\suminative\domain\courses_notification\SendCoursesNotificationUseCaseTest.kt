package com.thedasagroup.suminative.domain.courses_notification

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.model.request.print.CoursesNotificationRequest
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.response.courses_notification.CoursesNotificationResponse
import com.thedasagroup.suminative.data.repo.PrintRepository
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for SendCoursesNotificationUseCase
 */
class SendCoursesNotificationUseCaseTest {

    private lateinit var printRepository: PrintRepository
    private lateinit var useCase: SendCoursesNotificationUseCase

    @Before
    fun setup() {
        printRepository = mockk()
        useCase = SendCoursesNotificationUseCase(printRepository)
    }

    @Test
    fun `test sendCoursesNotification with request object returns success`() = runBlocking {
        // Arrange
        val request = CoursesNotificationRequest(
            storeId = 158,
            courseName = "Mains",
            tableName = "T-12",
            cartJson = """{"items":[{"id":201,"name":"Steak","qty":1,"price":19.99}]}"""
        )
        
        val expectedResponse = CoursesNotificationResponse(
            success = true,
            message = "Notification sent successfully",
            data = null
        )
        
        val mockFlow = MutableStateFlow<Async<CoursesNotificationResponse>>(Success(expectedResponse))
        
        coEvery { printRepository.sendCoursesNotification(request) } returns mockFlow

        // Act
        val result = useCase(request)
        val actualResponse = result.first()

        // Assert
        assertTrue(actualResponse is Success)
        assertEquals(expectedResponse, (actualResponse as Success).invoke())
    }

    @Test
    fun `test sendCoursesNotification with individual parameters`() = runBlocking {
        // Arrange
        val storeId = 158
        val courseName = "Starters"
        val tableName = "T-05"
        val cartJson = """{"items":[{"id":101,"name":"Soup","qty":2,"price":8.50}]}"""
        
        val expectedResponse = CoursesNotificationResponse(
            success = true,
            message = "Notification sent successfully",
            data = null
        )
        
        val mockFlow = MutableStateFlow<Async<CoursesNotificationResponse>>(Success(expectedResponse))
        
        coEvery { 
            printRepository.sendCoursesNotification(storeId, courseName, tableName, cartJson) 
        } returns mockFlow

        // Act
        val result = useCase(storeId, courseName, tableName, cartJson)
        val actualResponse = result.first()

        // Assert
        assertTrue(actualResponse is Success)
        assertEquals(expectedResponse, (actualResponse as Success).invoke())
    }

    @Test
    fun `test sendCoursesNotification with Cart list`() = runBlocking {
        // Arrange
        val storeId = 158
        val courseName = "Desserts"
        val tableName = "T-08"
        val carts = listOf(
            Cart(
                storeItem = StoreItem(id = 301, name = "Ice Cream"),
                quantity = 1,
                price = 5.99
            ),
            Cart(
                storeItem = StoreItem(id = 302, name = "Cake"),
                quantity = 2,
                price = 12.50
            )
        )
        
        val expectedResponse = CoursesNotificationResponse(
            success = true,
            message = "Notification sent successfully",
            data = null
        )
        
        val mockFlow = MutableStateFlow<Async<CoursesNotificationResponse>>(Success(expectedResponse))
        
        coEvery { 
            printRepository.sendCoursesNotification(storeId, courseName, tableName, carts) 
        } returns mockFlow

        // Act
        val result = useCase(storeId, courseName, tableName, carts)
        val actualResponse = result.first()

        // Assert
        assertTrue(actualResponse is Success)
        assertEquals(expectedResponse, (actualResponse as Success).invoke())
    }
}
