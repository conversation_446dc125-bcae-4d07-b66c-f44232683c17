package com.thedasagroup.suminative.ui.products

import androidx.lifecycle.viewModelScope
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.airbnb.mvrx.mocking.MockableMavericks
import com.airbnb.mvrx.mocking.MockableMavericks.setState
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.cloud_print.CloudPrintRequest
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.OptionSet
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem2
import com.thedasagroup.suminative.data.model.request.print.PrintBillRequest
import com.thedasagroup.suminative.data.model.request.print.SendToKitchenRequest
import com.thedasagroup.suminative.data.model.request.sales.SalesRequest
import com.thedasagroup.suminative.data.model.response.courses_notification.CoursesNotificationResponse
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.order.OrderResponse2
import com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse
import com.thedasagroup.suminative.data.model.response.sales.SalesResponse
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.model.response.store_orders.Option
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.PrintRepository
import com.thedasagroup.suminative.data.repo.ProductRepository
import com.thedasagroup.suminative.domain.courses_notification.SendCoursesNotificationUseCase
import com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase
import com.thedasagroup.suminative.domain.orders.CreateOrderUseCase
import com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase
import com.thedasagroup.suminative.ui.sales.TotalSalesUseCase
import com.thedasagroup.suminative.ui.stock.StockUseCase
import com.thedasagroup.suminative.ui.utils.transformDecimal
import com.thedasagroup.suminative.ui.products.cart.CourseStatus
import com.thedasagroup.suminative.work.OrderSyncManager
import com.thedasagroup.suminative.work.SyncStatus
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

// Data class for Meal Course
data class MealCourse(
    val id: String,
    val name: String,
    val displayName: String
)

// Extended Cart item with meal course assignment
data class CartItemWithCourse(
    val cart: Cart,
    val courseId: String = "" // No default course initially
)

// Filter options for viewing courses
enum class CourseFilter(val displayName: String) {
    ALL("All"),
    STARTERS("Starters"),
    MAINS("Mains"),
    DESSERTS("Desserts")
}

class ProductsScreenViewModel @AssistedInject constructor(
    @Assisted state: ProductsScreenState,
    val prefs: Prefs,
    val stockUseCase: StockUseCase,
    val orderUseCase: PlaceOnlineOrderUseCase,
    val offlineOrderUseCase: OrderUseCase,
    val cloudPrintUseCase: CloudPrintUseCase,
    val getOptionDetailsUseCase: OptionDetailsUseCase,
    val salesUseCase: TotalSalesUseCase,
    val salesReportUseCase: GetSalesReportUseCase,
    val trueTimeImpl: TrueTimeImpl,
    val productsRepository: ProductRepository,
    val downloadProductsUseCase: DownloadProductsUseCase,
    val orderSyncManager: OrderSyncManager,
    val sendCoursesNotificationUseCase: SendCoursesNotificationUseCase,
    val printRepository: PrintRepository
) : MavericksViewModel<ProductsScreenState>(state) {

    init {
        monitorSyncStatus()
    }

    suspend fun getStockItems(): StateFlow<Async<StockItemsResponse>> {
        val flow = MutableStateFlow<Async<StockItemsResponse>>(Loading())
        setState {
            copy(stockItemsResponse = Loading())
        }
        stockUseCase().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(stockItemsResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(stockItemsResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun refreshProducts(): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        setState {
            copy(refreshing = true)
        }

        downloadProductsUseCase.refreshProducts().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    viewModelScope.launch {
                        getStockItems()
                    }
                    // Refresh the stock items after successful product refresh
                    copy(refreshing = false)
                }

                else -> {
                    flow.value = Uninitialized
                    copy(refreshing = false)
                }
            }
        }
        return flow
    }

    suspend fun getTotalSales(request: SalesRequest): StateFlow<Async<SalesResponse>> {
        val flow = MutableStateFlow<Async<SalesResponse>>(Loading())
        setState {
            copy(salesResponse = Loading())
        }
        salesUseCase(request = request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(salesResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(salesResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun getSalesReport(request: SalesRequest): StateFlow<Async<SalesReportResponse>> {
        val flow = MutableStateFlow<Async<SalesReportResponse>>(Loading())
        setState {
            copy(salesReportResponse = Loading(), salesRequest = request)
        }
        salesReportUseCase(request = request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(salesReportResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(salesReportResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    fun updateStock(
        order: Order, stock: Int, optionDetails: OptionDetails, stockItem: StoreItem,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val updatedStock = stockItem.copy(quantity = stock)
        val updatedOrder = targetOrder.carts?.map {
            // Check both store item ID and options to identify the specific cart item
            if (it.storeItem?.id == stockItem.id &&
                areOptionSetsEqual(it.storeItem?.optionSets, stockItem.optionSets)
            ) {
                it.copy(storeItem = updatedStock)
            } else {
                it
            }
        }

        val finalOrder = targetOrder.copy(carts = updatedOrder)

        setState {
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder
                copy(
                    stock = stock,
                    tableOrders = updatedTableOrders,
                    productTotal = calculateTotal(
                        stockItem = stockItem, optionDetails = optionDetails, updatedStock = stock
                    ).billAmount ?: 0.0
                )
            } else {
                // Fallback to global cart
                copy(
                    stock = stock,
                    order = finalOrder,
                    productTotal = calculateTotal(
                        stockItem = stockItem, optionDetails = optionDetails, updatedStock = stock
                    ).billAmount ?: 0.0
                )
            }
        }
    }


    fun resetStock() {
        setState {
            copy(
                stock = 1,
                optionDetailsResponse = Uninitialized,
                productTotal = 0.0
            )
        }
    }


    fun updateCartStock(
        state: ProductsScreenState,
        order: Order, stock: Int, stockItem: StoreItem, optionDetails: OptionDetails,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val updatedStock = stockItem.copy(quantity = stock)
        val updatedOrder = targetOrder.carts?.map {
            // Check both store item ID and options to identify the specific cart item
            if (it.storeItem?.id == stockItem.id &&
                areOptionSetsEqual(it.storeItem?.optionSets, stockItem.optionSets)
            ) {
                // Update the item-level totals based on new quantity
                val unitPrice = (it.netPayable ?: 0.0) / (it.quantity ?: 1)
                val unitTax = (it.tax ?: 0.0) / (it.quantity ?: 1)
                val newNetPayable = unitPrice * stock
                val newTax = unitTax * stock

                it.copy(
                    quantity = stock,
                    netPayable = newNetPayable,
                    tax = newTax,
                    storeItem = updatedStock
                )
            } else {
                it
            }
        }

        // Recalculate order totals
        val netPayable = updatedOrder?.sumByDouble { it.netPayable ?: 0.0 } ?: 0.0
        val totalTax = updatedOrder?.sumByDouble { it.tax ?: 0.0 } ?: 0.0
        val totalPrice = netPayable + totalTax

        val finalOrder = targetOrder.copy(
            carts = updatedOrder,
            netPayable = netPayable,
            tax = totalTax,
            totalPrice = totalPrice
        )

        setState {
            // Ensure at least one course exists before assigning cart items
            val stateWithCourse = state

            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = stateWithCourse.tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder

                // Table-specific sync
                val currentOrder = finalOrder
                val existingAssignments =
                    stateWithCourse.cartItemsWithCourses[currentTableId]?.associateBy { it.cart.storeItem?.id }
                        ?: emptyMap()

                val updatedCartItems = currentOrder.carts?.map { cart ->
                    existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = if (stateWithCourse.selectedCourseForNewItems.isNotEmpty()) {
                            stateWithCourse.selectedCourseForNewItems
                        } else {
                            stateWithCourse.availableCourses.firstOrNull()?.id ?: ""
                        }
                    )
                } ?: emptyList()

                val updatedCartItemsWithCourses = stateWithCourse.cartItemsWithCourses.toMutableMap()
                updatedCartItemsWithCourses[currentTableId] = updatedCartItems

                stateWithCourse.copy(
                    tableOrders = updatedTableOrders,
                    productTotal = calculateTotal(
                        stockItem = stockItem, optionDetails = optionDetails, updatedStock = stock
                    ).billAmount ?: 0.0,
                    stock = stock,
                    cartItemsWithCourses = updatedCartItemsWithCourses
                )

            } else {

                // Global sync when no tables
                val currentOrder = finalOrder
                val existingAssignments =
                    stateWithCourse.globalCartItemsWithCourses.associateBy { it.cart.storeItem?.id }

                val updatedGlobalCartItems = currentOrder.carts?.map { cart ->
                    existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = if (stateWithCourse.selectedCourseForNewItems.isNotEmpty()) {
                            stateWithCourse.selectedCourseForNewItems
                        } else {
                            stateWithCourse.availableCourses.firstOrNull()?.id ?: ""
                        }
                    )
                } ?: emptyList()

                stateWithCourse.copy(
                    order = finalOrder,
                    productTotal = calculateTotal(
                        stockItem = stockItem, optionDetails = optionDetails, updatedStock = stock
                    ).billAmount ?: 0.0,
                    stock = stock,
                    globalCartItemsWithCourses = updatedGlobalCartItems
                )
            }
        }

        // Sync course assignments after updating cart stock
        syncCartItemsWithCourses()
    }

    fun updateCartItemNotes(
        order: Order, cart: Cart, notes: String,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order
        }

        val updatedOrder = targetOrder.carts?.map {
            // Check both store item ID and options to identify the specific cart item
            if (it.storeItem?.id == cart.storeItem?.id &&
                areOptionSetsEqual(it.storeItem?.optionSets, cart.storeItem?.optionSets)
            ) {
                it.copy(notes = notes)
            } else {
                it
            }
        }

        val finalOrder = targetOrder.copy(carts = updatedOrder)

        setState {
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder
                copy(tableOrders = updatedTableOrders)
            } else {
                // Fallback to global cart
                copy(order = finalOrder)
            }
        }
    }

    fun voidCartItem(
        order: Order, cart: Cart,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        selectedTableIndex: Int,
        tableOrders: Map<Int, Order>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTables = selectedTables,
            selectedTableIndex = selectedTableIndex
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }
        val updatedOrder = targetOrder.carts?.map {
            // Check both store item ID and options to identify the specific cart item
            if (it.storeItem?.id == cart.storeItem?.id &&
                areOptionSetsEqual(it.storeItem?.optionSets, cart.storeItem?.optionSets)
            ) {
                // Create a voided version of the cart item with all prices set to 0 (keep quantity unchanged)
                val voidedStoreItem = it.storeItem?.copy(
                    price = 0.0,
                    billAmount = 0.0,
                    tax = 0.0,
                    discountedAmount = 0.0,
                    optionSets = it.storeItem.optionSets?.map { optionSet ->
                        optionSet.copy(
                            options = optionSet.options.map { option ->
                                option?.copy(price = 0.0)
                            } ?: mutableListOf()
                        )
                    }
                )

                it.copy(
                    storeItem = voidedStoreItem,
                    price = 0.0,
                    netPayable = 0.0,
                    tax = 0.0,
                    extraPrice = 0.0,
                    optionPrice = 0.0,
                    discount = 0.0,
                    notes = (it.notes
                        ?: "") + if (it.notes.isNullOrEmpty()) "VOIDED" else " - VOIDED"
                )
            } else {
                it
            }
        }

        // Recalculate order totals
        val netPayable = updatedOrder?.sumOf { it.netPayable ?: 0.0 } ?: 0.0
        val totalTax = updatedOrder?.sumOf { it.tax ?: 0.0 } ?: 0.0
        val totalPrice = netPayable + totalTax

        setState {
            if (currentTableId != null) {
                val finalOrder = order.copy(
                    carts = updatedOrder,
                    netPayable = netPayable,
                    tax = totalTax,
                    totalPrice = totalPrice
                )
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder

                val currentOrder = finalOrder
                val existingAssignments =
                    cartItemsWithCourses[currentTableId]?.associateBy { it.cart.storeItem?.id }
                        ?: emptyMap()

                val updatedCartItems = currentOrder.carts?.map { cart ->
                    val course = existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = if (selectedCourseForNewItems.isNotEmpty()) selectedCourseForNewItems else availableCourses.firstOrNull()?.id ?: ""
                    )
                    course.copy(cart = cart)
                } ?: emptyList()

                val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
                updatedCartItemsWithCourses[currentTableId] = updatedCartItems

                copy(
                    tableOrders = updatedTableOrders,
                    order = order.copy(
                        carts = updatedOrder,
                        netPayable = netPayable,
                        tax = totalTax,
                        totalPrice = totalPrice
                    ),
                    cartItemsWithCourses = updatedCartItemsWithCourses
                )
            } else {

                val currentOrder = order.copy(
                    carts = updatedOrder,
                    netPayable = netPayable,
                    tax = totalTax,
                    totalPrice = totalPrice
                )
                val existingAssignments =
                    globalCartItemsWithCourses.associateBy { it.cart.storeItem?.id }

                val updatedGlobalCartItems = currentOrder.carts?.map { cart ->
                    val course = existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = "course_1"
                    )
                    course.copy(cart = cart)
                } ?: emptyList()

                // Fallback to global cart
                copy(
                    order = currentOrder,
                    globalCartItemsWithCourses = updatedGlobalCartItems,
                )
            }
        }
    }

    fun addItemToCart(
        order: Order, stockItem: StockItem, optionDetails: OptionDetails, quantity: Int,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTables = selectedTables,
            selectedTableIndex = selectedTableIndex
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val listCart = targetOrder.carts?.toMutableList() ?: mutableListOf()
        val storeItem = stockItem.toStoreItem()
        val updatedStoreItem = storeItem.copy(
            optionSets = optionDetails.optionSets ?: mutableListOf(),
            quantity = quantity,
            billAmount = calculateTotal(
                stockItem = storeItem, optionDetails = optionDetails, updatedStock = quantity
            ).billAmount ?: 0.0
        )

        // Check if item with same ID and options already exists in cart
        val existingCartItemIndex = listCart.indexOfFirst { cartItem ->
            cartItem.storeItem?.id == stockItem.id &&
                    areOptionSetsEqual(cartItem.storeItem?.optionSets, optionDetails.optionSets)
        }

        if (existingCartItemIndex != -1) {
            // Item already exists, increment its quantity
            val existingCartItem = listCart[existingCartItemIndex]
            val newQuantity = (existingCartItem.quantity ?: 0) + quantity

            // Recalculate totals for the updated quantity
            val updatedStoreItemForExisting = existingCartItem.storeItem?.copy(
                quantity = newQuantity,
                billAmount = calculateTotal(
                    stockItem = existingCartItem.storeItem,
                    optionDetails = optionDetails,
                    updatedStock = newQuantity
                ).billAmount ?: 0.0
            )

            val itemPrice =
                updatedStoreItemForExisting?.price?.transformDecimal()?.toDouble() ?: 0.0
            val itemTax = updatedStoreItemForExisting?.tax?.transformDecimal()?.toDouble() ?: 0.0
            val itemDiscount =
                updatedStoreItemForExisting?.discountedAmount?.transformDecimal()?.toDouble() ?: 0.0
            val itemNetPayable =
                updatedStoreItemForExisting?.billAmount?.transformDecimal()?.toDouble() ?: 0.0

            // Update the existing cart item
            listCart[existingCartItemIndex] = existingCartItem.copy(
                storeItem = updatedStoreItemForExisting,
                quantity = newQuantity,
                price = itemPrice,
                tax = itemTax,
                discount = itemDiscount,
                netPayable = itemNetPayable
            )
        } else {
            // Item doesn't exist, add new cart item
            val itemPrice = updatedStoreItem.price?.transformDecimal()?.toDouble() ?: 0.0
            val itemTax = updatedStoreItem.tax?.transformDecimal()?.toDouble() ?: 0.0
            val itemDiscount =
                updatedStoreItem.discountedAmount?.transformDecimal()?.toDouble() ?: 0.0
            val itemNetPayable = updatedStoreItem.billAmount?.transformDecimal()?.toDouble() ?: 0.0

            listCart.add(
                Cart(
                    storeItem = updatedStoreItem,
                    quantity = quantity,
                    price = itemPrice,
                    extraPrice = 0.0,
                    tax = itemTax,
                    discount = itemDiscount,
                    netPayable = itemNetPayable,
                    optionPrice = 0.0,
                    isB1G1 = false
                )
            )
        }

        // Calculate order-level totals
        val netPayable = listCart.sumByDouble { it.netPayable ?: 0.0 }
        val totalTax = listCart.sumByDouble { it.tax ?: 0.0 }
        val totalPrice = netPayable + totalTax

        val updatedOrder = targetOrder.copy(
            carts = listCart,
            netPayable = netPayable,
            tax = totalTax,
            totalPrice = totalPrice
        )

        setState {
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = updatedOrder
                copy(
                    tableOrders = updatedTableOrders,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
            } else {
                // Fallback to global cart
                copy(
                    order = updatedOrder,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
            }
        }

        // Sync course assignments after adding item
        syncCartItemsWithCourses()
    }

    /**
     * Helper function to compare two option sets for equality
     * Used to determine if an item with the same options already exists in cart
     */
    private fun areOptionSetsEqual(
        optionSets1: List<OptionSet>?,
        optionSets2: List<OptionSet>?
    ): Boolean {
        if (optionSets1 == null && optionSets2 == null) return true
        if (optionSets1 == null || optionSets2 == null) return false
        if (optionSets1.size != optionSets2.size) return false

        // Sort both lists by ID for consistent comparison
        val sorted1 = optionSets1.sortedBy { it.id }
        val sorted2 = optionSets2.sortedBy { it.id }

        return sorted1.zip(sorted2).all { (set1, set2) ->
            set1.id == set2.id && areOptionsEqual(set1.options, set2.options)
        }
    }

    /**
     * Helper function to compare two option lists for equality
     */
    private fun areOptionsEqual(
        options1: List<Option?>?,
        options2: List<Option?>?
    ): Boolean {
        if (options1 == null && options2 == null) return true
        if (options1 == null || options2 == null) return false
        if (options1.size != options2.size) return false

        // Sort both lists by ID for consistent comparison
        val sorted1 = options1.filterNotNull().sortedBy { it.id }
        val sorted2 = options2.filterNotNull().sortedBy { it.id }

        return sorted1.zip(sorted2).all { (option1, option2) ->
            option1.id == option2.id &&
                    option1.optionchecked == option2.optionchecked &&
                    option1.quantity == option2.quantity
        }
    }

    /**
     * Extension function to convert StoreItem to StockItem
     * Used for calculations when updating existing cart items
     */
    private fun StoreItem.toStockItem(): StockItem {
        return StockItem(
            additionalInfo = this.additionalInfo,
            billAmount = this.billAmount,
            brandId = this.brandId,
            businessId = this.businessId,
            categoryId = this.categoryId,
            createdBy = this.createdBy,
            createdOn = this.createdOn,
            dailyCapacity = this.dailyCapacity,
            description = this.description,
            discountType = this.discountType,
            discountedAmount = this.discountedAmount?.toString(),
            id = this.id,
            ingredients = this.ingredients,
            modifiedBy = this.modifiedBy,
            modifiedOn = this.modifiedOn,
            name = this.name,
            pic = this.pic,
            preparationTime = this.preparationTime,
            price = this.price,
            servingSize = this.servingSize,
            stock = this.quantity,
            storeId = this.storeId,
            tax = this.tax,
            unitId = this.unitId,
            vat = this.vat
        )
    }

    fun removeItemFromCart(
        order: Order,
        stockItem: StoreItem,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ): StateFlow<Async<Order>> {
        val flow = MutableStateFlow<Async<Order>>(Loading())
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val listCart = targetOrder.carts?.toMutableList() ?: mutableListOf()
        listCart.removeAll {
            it.storeItem?.uuid == stockItem.uuid
        }
        val netPayable = listCart.sumByDouble { it.netPayable ?: 0.0 }
        val totalTax = listCart.sumByDouble { it.tax ?: 0.0 }
        val totalPrice = netPayable + totalTax
        val updatedOrder = targetOrder.copy(
            carts = listCart,
            netPayable = netPayable,
            tax = totalTax,
            totalPrice = totalPrice
        )

        setState {
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = updatedOrder
                copy(
                    tableOrders = updatedTableOrders,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
            } else {
                // Fallback to global cart
                copy(
                    order = updatedOrder,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
            }
        }

        // Sync course assignments after removing item
        syncCartItemsWithCourses()

        flow.value = Success(updatedOrder)
        return flow
    }

    fun updateProductDetailsBottomSheetVisibility(stockItem: StockItem?) {
        setState {
            copy(isBottomSheetVisible = stockItem)
        }
    }

    suspend fun placeOrder(order: Order, transId: String): StateFlow<Async<OrderResponse2>> {
        val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
        setState {
            copy(orderResponse = Loading())
        }
        orderUseCase(order, transId = transId).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    viewModelScope.launch {
                        orderSyncManager.triggerImmediateSync()
                    }
                    copy(orderResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(orderResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun getOptionDetails(
        itemId: Int, stockItem: StockItem
    ): StateFlow<Async<OptionDetails>> {
        val flow = MutableStateFlow<Async<OptionDetails>>(Loading())
        setState {
            copy(optionDetailsResponse = Loading())
        }
        getOptionDetailsUseCase(itemId = itemId).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        optionDetailsResponse = it(), productTotal = calculateTotal(
                            stockItem = stockItem.toStoreItem(),
                            optionDetails = it()() ?: OptionDetails(),
                            updatedStock = 1
                        ).billAmount ?: 0.0
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(optionDetailsResponse = Uninitialized)
                }
            }
        }
        return flow
    }


    fun updateSelectedOptionCondition1(
        option: Option,
        stock: Int,
        stockItem: StockItem,
        optionDetails: OptionDetails,
        currentOptionSet: OptionSet
    ) {

        val options = optionDetails.optionSets?.flatMap { optionSet ->
            if (optionSet.id == currentOptionSet.id) {
                optionSet.options.map { currentOption ->
                    if (currentOption?.id == option.id) {
                        currentOption?.copy(
                            optionchecked = true, quantity = 1
                        )
                    } else {
                        currentOption?.copy(optionchecked = false, quantity = 0)
                    }
                }
            } else mutableListOf()
        }
        val optionSets = optionDetails.optionSets?.map {
            if (it.id == currentOptionSet.id) {
                it.copy(options = options ?: mutableListOf())
            } else {
                it
            }
        }
        val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)
        setState {
            copy(
                optionDetailsResponse = Success(updatedOptionDetails),
                productTotal = calculateTotal(
                    stockItem.toStoreItem(), updatedOptionDetails, updatedStock = stock
                ).billAmount ?: 0.0
            )
        }
    }

    fun addSelectedOptionCondition2(
        optionDetails: OptionDetails,
        currentOptionSet: OptionSet,
        option: Option,
        stockItem: StoreItem,
        stock: Int
    ) {
        val options = optionDetails.optionSets?.flatMap { optionSet ->
            if (optionSet.id == currentOptionSet.id) {
                optionSet.options.map { currentOption ->
                    if (currentOption?.id == option.id) {
                        currentOption?.copy(
                            optionchecked = true, quantity = 1
                        )
                    } else {
                        currentOption
                    }
                }
            } else mutableListOf()
        }
        val optionSets = optionDetails.optionSets?.map {
            if (it.id == currentOptionSet.id) {
                it.copy(options = options ?: mutableListOf())
            } else {
                it
            }
        }
        val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)
        setState {
            copy(
                optionDetailsResponse = Success(updatedOptionDetails),
                productTotal = calculateTotal(
                    stockItem, updatedOptionDetails, updatedStock = stock
                ).billAmount ?: 0.0
            )
        }
    }

    fun removeSelectedOptionCondition2(
        optionDetails: OptionDetails,
        option: Option,
        currentOptionSet: OptionSet,
        stockItem: StoreItem,
        stock: Int
    ) {
        val options = optionDetails.optionSets?.flatMap { optionSet ->
            if (optionSet.id == currentOptionSet.id) {
                optionSet.options.map { currentOption ->
                    if (currentOption?.id == option.id) {
                        currentOption?.copy(optionchecked = false, quantity = 0)
                    } else {
                        currentOption
                    }
                }
            } else {
                mutableListOf()
            }
        }
        val optionSets = optionDetails.optionSets?.map {
            if (it.id == currentOptionSet.id) {
                it.copy(options = options ?: mutableListOf())
            } else {
                it
            }
        }

        val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)

        setState {
            copy(
                optionDetailsResponse = Success(updatedOptionDetails),
                productTotal = calculateTotal(
                    stockItem, updatedOptionDetails, updatedStock = stock
                ).billAmount ?: 0.0,
            )
        }
    }

    fun calculateTotal(
        stockItem: StoreItem, optionDetails: OptionDetails, updatedStock: Int
    ): StoreItem {
        if (optionDetails.optionSets?.isNotEmpty() == true) {
            val optionPrice = optionDetails.optionSets.flatMap { optionSet ->
                optionSet.options.map { option ->
                    if (option?.optionchecked == true) {
                        (option.price ?: 0.0) * (option.quantity?.toDouble() ?: 0.0)
                    } else {
                        0.0
                    }
                }
            }.sum()
            val price = stockItem.price ?: 0.0
            val tax = stockItem.tax ?: 0.0
            val discount = stockItem.discountedAmount ?: 0.0
            val total = (price + tax - discount + optionPrice) * (updatedStock)
            return stockItem.copy(
                price = price, tax = tax, discountedAmount = discount, billAmount = total
            )
        } else {
            val price = stockItem.price ?: 0.0
            val tax = stockItem.tax ?: 0.0
            val discount = stockItem.discountedAmount ?: 0.0
            val total = (price + tax - discount) * (updatedStock)
            return stockItem.copy(
                price = price, tax = tax, discountedAmount = discount, billAmount = total
            )
        }
    }

    fun updateOptionStock(
        option: Option,
        currentOptionSet: OptionSet,
        stock: Int,
        optionStock: Int,
        optionDetails: OptionDetails,
        stockItem: StoreItem
    ) {
        val options = optionDetails.optionSets?.flatMap { optionSet ->
            if (optionSet.id == currentOptionSet.id) {
                optionSet.options.map { currentOption ->
                    if (currentOption?.id == option.id) {
                        if (optionStock > 0) {
                            currentOption?.copy(optionchecked = true, quantity = optionStock)
                        } else {
                            currentOption?.copy(optionchecked = false, quantity = optionStock)
                        }
                    } else {
                        currentOption
                    }
                }
            } else {
                mutableListOf()
            }
        }
        val optionSets = optionDetails.optionSets?.map {
            if (it.id == currentOptionSet.id) {
                it.copy(options = options ?: mutableListOf())
            } else {
                it
            }
        }


        val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)

        setState {
            copy(
                optionDetailsResponse = Success(updatedOptionDetails),
                stock = stock,
                productTotal = calculateTotal(
                    stockItem, updatedOptionDetails, updatedStock = stock
                ).billAmount ?: 0.0
            )
        }
    }

    fun updateShowPrintingPreview(order: OrderItem2?, shouldPrintInstant: Boolean = false) {
        setState {
            copy(isShowPrintingPreview = order, shouldPrintInstant = shouldPrintInstant)
        }
    }

    fun showSalesReportDialog(show: Boolean) {
        setState {
            copy(showSalesReportDialog = show)
        }
    }

    fun updateOrder(order: Order) {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = order
                copy(tableOrders = updatedTableOrders)
            } else {
                // Fallback to global cart
                copy(order = order)
            }
        }
    }

    fun updateCartVisibility(visible: Boolean) {
        setState {
            copy(showCart = visible)
        }
    }

    fun clearCart() {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Clear the current table's cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = Order(
                    carts = emptyList(),
                    netPayable = 0.0,
                    tax = 0.0,
                    totalPrice = 0.0
                )
                copy(tableOrders = updatedTableOrders)
            } else {
                // Fallback to clearing global cart if no table selected
                copy(
                    order = Order(
                        carts = emptyList(),
                        netPayable = 0.0,
                        tax = 0.0,
                        totalPrice = 0.0
                    )
                )
            }
        }
    }

    /**
     * Update the order for a specific table
     */
    private fun updateTableOrder(tableId: Int, updatedOrder: Order) {
        setState {
            val updatedTableOrders = tableOrders.toMutableMap()
            updatedTableOrders[tableId] = updatedOrder
            copy(tableOrders = updatedTableOrders)
        }
    }

    /**
     * Get the current table's order
     */
//    private fun getCurrentTableOrder(): Order {
//        val currentTableId = getCurrentTableId()
//        return if (currentTableId != null) {
//            tableOrders[currentTableId] ?: Order()
//        } else {
//            order // Fallback to global order
//        }
//    }

    /**
     * Update course assignment for a cart item
     */
    fun updateCartItemCourse(cartItemId: Int?, newCourseId: String) {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null && cartItemId != null) {
                // Table-specific course assignment
                val currentCartItems = cartItemsWithCourses[currentTableId] ?: emptyList()
                val updatedCartItems = currentCartItems.map { item ->
                    if (item.cart.storeItem?.id == cartItemId) {
                        item.copy(courseId = newCourseId)
                    } else {
                        item
                    }
                }
                val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
                updatedCartItemsWithCourses[currentTableId] = updatedCartItems
                copy(cartItemsWithCourses = updatedCartItemsWithCourses)
            } else if (cartItemId != null) {
                // Global course assignment when no tables
                val updatedGlobalCartItems = globalCartItemsWithCourses.map { item ->
                    if (item.cart.storeItem?.id == cartItemId) {
                        item.copy(courseId = newCourseId)
                    } else {
                        item
                    }
                }
                copy(globalCartItemsWithCourses = updatedGlobalCartItems)
            } else {
                this
            }
        }
    }

    /**
     * Update course filter for current table
     */
    fun updateCourseFilter(filter: CourseFilter) {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Table-specific filter
                val updatedFilters = selectedCourseFilter.toMutableMap()
                updatedFilters[currentTableId] = filter
                copy(selectedCourseFilter = updatedFilters)
            } else {
                // Global filter when no tables
                copy(globalSelectedCourseFilter = filter)
            }
        }
    }

    /**
     * Sync cart items with course assignments when order changes
     */
    fun syncCartItemsWithCourses() {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Table-specific sync
                val currentOrder = tableOrders[currentTableId] ?: Order()
                val existingAssignments =
                    cartItemsWithCourses[currentTableId]?.associateBy { it.cart.storeItem?.id }
                        ?: emptyMap()

                val updatedCartItems = currentOrder.carts?.map { cart ->
                    val course = existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = if (selectedCourseForNewItems.isNotEmpty()) selectedCourseForNewItems else availableCourses.firstOrNull()?.id ?: ""
                    )
                    course.copy(cart = cart)
                } ?: emptyList()

                val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
                updatedCartItemsWithCourses[currentTableId] = updatedCartItems
                copy(cartItemsWithCourses = updatedCartItemsWithCourses)
            } else {
                // Global sync when no tables
                val currentOrder = order
                val existingAssignments =
                    globalCartItemsWithCourses.associateBy { it.cart.storeItem?.id }

                val updatedGlobalCartItems = currentOrder.carts?.map { cart ->
                    val course = existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = if (selectedCourseForNewItems.isNotEmpty()) selectedCourseForNewItems else availableCourses.firstOrNull()?.id ?: ""
                    )
                    course.copy(cart = cart)
                } ?: emptyList()

                copy(globalCartItemsWithCourses = updatedGlobalCartItems)
            }
        }
    }


    /**
     * Initialize course assignments for a new table
     */
    fun initializeTableCourses(tableId: Int) {
        setState {
            val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
            val updatedFilters = selectedCourseFilter.toMutableMap()

            if (!updatedCartItemsWithCourses.containsKey(tableId)) {
                updatedCartItemsWithCourses[tableId] = emptyList()
            }
            if (!updatedFilters.containsKey(tableId)) {
                updatedFilters[tableId] = CourseFilter.ALL
            }

            copy(
                cartItemsWithCourses = updatedCartItemsWithCourses,
                selectedCourseFilter = updatedFilters
            )
        }
    }

    /**
     * Get the current table ID
     */
    private fun getCurrentTableId(
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ): Int? {
        return if (selectedTables.isNotEmpty() && selectedTableIndex < selectedTables.size) {
            selectedTables[selectedTableIndex].tableId
        } else {
            null
        }
    }

    /**
     * Add a selected table to the list
     */
    fun addSelectedTable(selection: AreaTableSelectionHelper.AreaTableSelection) {
        setState {
            val updatedTables = selectedTables.toMutableList()
            // Check if table is already selected
            if (!updatedTables.any { it.tableId == selection.tableId }) {
                updatedTables.add(selection)
                // Initialize empty cart for new table if it doesn't exist
                val updatedTableOrders = tableOrders.toMutableMap()
                if (!updatedTableOrders.containsKey(selection.tableId)) {
                    updatedTableOrders[selection.tableId] = Order()
                }

                // Initialize course state for new table
                val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
                val updatedFilters = selectedCourseFilter.toMutableMap()
                if (!updatedCartItemsWithCourses.containsKey(selection.tableId)) {
                    updatedCartItemsWithCourses[selection.tableId] = emptyList()
                }
                if (!updatedFilters.containsKey(selection.tableId)) {
                    updatedFilters[selection.tableId] = CourseFilter.ALL
                }

                copy(
                    selectedTables = updatedTables,
                    selectedTableIndex = updatedTables.size - 1, // Select the newly added table
                    tableOrders = updatedTableOrders,
                    cartItemsWithCourses = updatedCartItemsWithCourses,
                    selectedCourseFilter = updatedFilters,
                    showCart = updatedTableOrders[selection.tableId]?.carts?.isNotEmpty() ?: false
                )
            } else {
                // If table already exists, just select it
                val existingIndex = updatedTables.indexOfFirst { it.tableId == selection.tableId }
                val tableOrder = tableOrders[selection.tableId] ?: Order()
                copy(
                    selectedTableIndex = existingIndex,
                    showCart = tableOrder.carts?.isNotEmpty() ?: false
                )
            }
        }
    }

    /**
     * Remove a selected table from the list
     */
    fun removeSelectedTable(tableId: Int) {
        setState {
            val updatedTables = selectedTables.filter { it.tableId != tableId }
            val updatedTableOrders = tableOrders.toMutableMap()
            // Remove the table's cart data
            updatedTableOrders.remove(tableId)

            val newSelectedIndex = when {
                updatedTables.isEmpty() -> 0
                selectedTableIndex >= updatedTables.size -> updatedTables.size - 1
                else -> selectedTableIndex
            }

            // Update cart visibility based on new selected table
            val newTableOrder =
                if (updatedTables.isNotEmpty() && newSelectedIndex < updatedTables.size) {
                    val newTableId = updatedTables[newSelectedIndex].tableId
                    updatedTableOrders[newTableId] ?: Order()
                } else {
                    Order()
                }

            copy(
                selectedTables = updatedTables,
                selectedTableIndex = newSelectedIndex,
                tableOrders = updatedTableOrders,
                showCart = newTableOrder.carts?.isNotEmpty() ?: false
            )
        }
    }

    /**
     * Set the selected table index
     */
    fun setSelectedTableIndex(index: Int) {
        setState {
            if (index < selectedTables.size) {
                val newTableId = selectedTables[index].tableId
                val newTableOrder = tableOrders[newTableId] ?: Order()
                copy(
                    selectedTableIndex = index,
                    showCart = newTableOrder.carts?.isNotEmpty() ?: false
                )
            } else {
                copy(selectedTableIndex = index)
            }
        }
    }

    /**
     * Get the currently selected table
     */
//    fun getCurrentSelectedTable(): AreaTableSelectionHelper.AreaTableSelection? {
//        return withState(this) { state ->
//            if (state.selectedTables.isNotEmpty() && state.selectedTableIndex < state.selectedTables.size) {
//                state.selectedTables[state.selectedTableIndex]
//            } else null
//        }
//    }

    private fun monitorSyncStatus() {
        viewModelScope.launch {
            orderSyncManager.getLastSyncResult().collect { result ->
                setState {
                    copy(
                        syncStatus = when {
                            result == null -> SyncStatus.Idle
                            result.isSuccess -> SyncStatus.Success(
                                syncedCount = result.syncedCount,
                                totalCount = result.totalCount
                            )

                            else -> SyncStatus.Error(
                                errorMessage = result.errorMessage ?: "Unknown error",
                                failedCount = result.failedCount,
                                totalCount = result.totalCount
                            )
                        }
                    )
                }
            }
        }
    }

    suspend fun placeOrderOffline(order: Order): StateFlow<Async<OrderResponse2>> {
        val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
        setState {
            copy(orderResponse = Loading())
        }
        offlineOrderUseCase(order).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    viewModelScope.launch {
                        orderSyncManager.triggerImmediateSync()
                    }
                    copy(orderResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(orderResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun cloudPrint(request: CloudPrintRequest): StateFlow<Async<OrderResponse2>> {
        val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
        setState {
            copy(orderResponse = Loading())
        }
        cloudPrintUseCase(request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(orderResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(orderResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    fun isMyGuava(): Boolean {
        val storeId = prefs.store?.id
        return prefs.loginResponse?.stores?.firstOrNull { it.id == storeId }?.paymentProcessorName == "My Guava"
    }

    fun isSumUp(): Boolean {
        return false
    }


    fun getCourseCartItems(
        currentOrder: Order,
        existingAssignments: List<CartItemWithCourse>
    ): List<CartItemWithCourse> {
        val updatedCartItems = currentOrder.carts?.map { cart ->
            existingAssignments[cart.storeItem?.id ?: 0]
        } ?: emptyList()

        return updatedCartItems
    }

    /**
     * Add a new course to the available courses list
     */
    fun addNewCourse(courseName: String, availableCourses: List<MealCourse>) {
        setState {
            val newCourseId = "course_${courseName.lowercase().replace(" ", "_")}"
            val newCourse = MealCourse(
                id = newCourseId,
                name = courseName,
                displayName = courseName
            )
            val updatedCourses = availableCourses.toMutableList()
            updatedCourses.add(newCourse)
            copy(availableCourses = updatedCourses)
        }
    }

    /**
     * Add default courses (Starters, Mains, Desserts) to the available courses list
     * Only adds courses that don't already exist
     */
    fun addDefaultCourses() {
        setState {
            val defaultCourses = listOf(
                MealCourse("course_starters", "Starters", "Starters"),
                MealCourse("course_mains", "Mains", "Mains"),
                MealCourse("course_desserts", "Desserts", "Desserts")
            )

            val existingCourseIds = availableCourses.map { it.id }.toSet()
            val coursesToAdd = defaultCourses.filter { it.id !in existingCourseIds }

            if (coursesToAdd.isNotEmpty()) {
                val wasEmpty = availableCourses.isEmpty()
                val updatedCourses = availableCourses.toMutableList()
                updatedCourses.addAll(coursesToAdd)

                // If we're adding courses to an empty list, set the first course as active
                val currentTableId = getCurrentTableId()
                val updatedTableActiveCourses = if (wasEmpty && currentTableId != null) {
                    tableActiveCourses.toMutableMap().apply {
                        this[currentTableId] = updatedCourses.firstOrNull()?.id ?: ""
                    }
                } else {
                    tableActiveCourses
                }

                val newCurrentActiveCourse = if (wasEmpty && currentTableId == null) {
                    updatedCourses.firstOrNull()?.id
                } else {
                    currentActiveCourse
                }

                copy(
                    availableCourses = updatedCourses,
                    tableActiveCourses = updatedTableActiveCourses,
                    currentActiveCourse = newCurrentActiveCourse
                )
            } else {
                // No changes needed if all default courses already exist
                this
            }
        }
    }

    /**
     * Add a new numbered course automatically (Course 1, Course 2, etc.)
     */
    fun addNumberedCourse() {
        setState {
            val courseNumber = availableCourses.size + 1
            val courseName = "Course $courseNumber"
            val newCourseId = "course_$courseNumber"

            val newCourse = MealCourse(
                id = newCourseId,
                name = courseName,
                displayName = courseName
            )

            val updatedCourses = availableCourses.toMutableList()
            updatedCourses.add(newCourse)

            // If this is the first course, select it as the default for new items and set as active
            val newSelectedCourse = if (selectedCourseForNewItems.isEmpty()) {
                newCourseId
            } else {
                selectedCourseForNewItems
            }

            // If this is the first course, set it as active course
            val currentTableId = getCurrentTableId()
            val updatedTableActiveCourses = if (courseNumber == 1 && currentTableId != null) {
                tableActiveCourses.toMutableMap().apply {
                    this[currentTableId] = newCourseId
                }
            } else {
                tableActiveCourses
            }

            val newCurrentActiveCourse = if (courseNumber == 1 && currentTableId == null) {
                newCourseId
            } else {
                currentActiveCourse
            }

            copy(
                availableCourses = updatedCourses,
                selectedCourseForNewItems = newSelectedCourse,
                tableActiveCourses = updatedTableActiveCourses,
                currentActiveCourse = newCurrentActiveCourse
            )
        }
    }

    /**
     * Ensure at least one course exists, creating "Course 1" if needed
     */
    private fun ensureAtLeastOneCourse(state: ProductsScreenState): ProductsScreenState {
        return state
    }

    /**
     * Set the selected course for new items
     */
    fun setSelectedCourseForNewItems(courseId: String) {
        setState {
            copy(selectedCourseForNewItems = courseId)
        }
    }

    /**
     * Remove a course from available courses
     * @param courseId The ID of the course to remove
     * @return true if course was removed, false if it's the last course
     */
    fun removeCourse(courseId: String, state: ProductsScreenState): Boolean {
        return if (state.availableCourses.size <= 1) {
            // Cannot remove the last course
            false
        } else {
            setState {
                val updatedCourses = availableCourses.toMutableList()
                updatedCourses.removeAll { it.id == courseId }

                // If the removed course was the selected course for new items, select the first remaining course
                val newSelectedCourse = if (selectedCourseForNewItems == courseId) {
                    updatedCourses.firstOrNull()?.id ?: ""
                } else {
                    selectedCourseForNewItems
                }

                // Clean up course statuses and active courses for the removed course
                val updatedCourseStatuses = courseStatuses.toMutableMap()
                updatedCourseStatuses.remove(courseId)

                val updatedTableCourseStatuses =
                    tableCourseStatuses.mapValues { (_, courseStatuses) ->
                        courseStatuses.toMutableMap().apply { remove(courseId) }
                    }

                val updatedTableActiveCourses = tableActiveCourses.mapValues { (_, activeCourse) ->
                    if (activeCourse == courseId) {
                        updatedCourses.firstOrNull()?.id ?: ""
                    } else {
                        activeCourse
                    }
                }

                val newCurrentActiveCourse = if (currentActiveCourse == courseId) {
                    updatedCourses.firstOrNull()?.id
                } else {
                    currentActiveCourse
                }

                copy(
                    availableCourses = updatedCourses,
                    selectedCourseForNewItems = newSelectedCourse,
                    courseStatuses = updatedCourseStatuses,
                    tableCourseStatuses = updatedTableCourseStatuses,
                    tableActiveCourses = updatedTableActiveCourses,
                    currentActiveCourse = newCurrentActiveCourse
                )
            }
            true
        }
    }

    /**
     * Edit an existing course
     * @param courseId The ID of the course to edit
     * @param newName The new name for the course
     */
    fun editCourse(courseId: String, newName: String) {
        setState {
            val updatedCourses = availableCourses.map { course ->
                if (course.id == courseId) {
                    course.copy(name = newName, displayName = newName)
                } else {
                    course
                }
            }
            copy(availableCourses = updatedCourses)
        }
    }

    /**
     * Update the status of a specific course
     * @param courseId The ID of the course to update
     * @param status The new status for the course
     */
    fun updateCourseStatus(courseId: String, status: CourseStatus) {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Update table-specific course status
                val updatedTableStatuses = tableCourseStatuses.toMutableMap()
                val currentTableStatuses =
                    updatedTableStatuses[currentTableId]?.toMutableMap() ?: mutableMapOf()
                currentTableStatuses[courseId] = status
                updatedTableStatuses[currentTableId] = currentTableStatuses
                copy(tableCourseStatuses = updatedTableStatuses)
            } else {
                // Update global course status (for walk-in customers)
                val updatedStatuses = courseStatuses.toMutableMap()
                updatedStatuses[courseId] = status
                copy(courseStatuses = updatedStatuses)
            }
        }
    }

    /**
     * Get the status of a specific course
     * @param courseId The ID of the course
     * @return The current status of the course, defaults to GO if not set
     */
    fun getCourseStatus(courseId: String, state: ProductsScreenState): CourseStatus {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = state.selectedTableIndex,
            selectedTables = state.selectedTables
        )
        return if (currentTableId != null) {
            // Get table-specific course status
            state.tableCourseStatuses[currentTableId]?.get(courseId) ?: CourseStatus.GO
        } else {
            // Get global course status (for walk-in customers)
            state.courseStatuses[courseId] ?: CourseStatus.GO
        }
    }

    /**
     * Mark a course as complete
     * @param courseId The ID of the course to mark as complete
     */
    fun markCourseAsComplete(courseId: String) {
        updateCourseStatus(courseId, CourseStatus.COMPLETE)
    }

    /**
     * Reset a course status back to GO
     * @param courseId The ID of the course to reset
     */
    fun resetCourseStatus(courseId: String) {
        updateCourseStatus(courseId, CourseStatus.GO)
    }

    /**
     * Remove course statuses for a specific table
     * @param tableId The ID of the table to remove course statuses for
     */
    fun removeTableCourseStatuses(tableId: Int) {
        setState {
            val updatedTableStatuses = tableCourseStatuses.toMutableMap()
            updatedTableStatuses.remove(tableId)

            val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
            updatedTableActiveCourses.remove(tableId)

            copy(
                tableCourseStatuses = updatedTableStatuses,
                tableActiveCourses = updatedTableActiveCourses
            )
        }
    }

    /**
     * Clear all course statuses for all tables
     */
    fun clearAllTableCourseStatuses() {
        setState {
            copy(
                tableCourseStatuses = emptyMap(),
                tableActiveCourses = emptyMap()
            )
        }
    }

    /**
     * Get course statuses for the current table or global statuses
     * @return Map of course statuses for the current context
     */
    fun getCurrentCourseStatuses(state: ProductsScreenState): Map<String, CourseStatus> {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = state.selectedTableIndex,
            selectedTables = state.selectedTables
        )
        return if (currentTableId != null) {
            state.tableCourseStatuses[currentTableId] ?: emptyMap()
        } else {
            state.courseStatuses
        }
    }

    /**
     * Handle table selection change - initialize course statuses for new table if needed
     * @param tableId The ID of the newly selected table
     */
    fun onTableSelected(tableId: Int) {
        setState {
            val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
            // Always set first course as active for new table
            updatedTableActiveCourses[tableId] = availableCourses.firstOrNull()?.id ?: ""
            copy(tableActiveCourses = updatedTableActiveCourses)
        }
    }

    /**
     * Handle table removal - clean up course statuses for the removed table
     * @param tableId The ID of the table being removed
     */
    fun onTableRemoved(tableId: Int) {
        removeTableCourseStatuses(tableId)
    }

    /**
     * Reset course statuses for the current table
     */
    fun resetCurrentTableCourseStatuses(state: ProductsScreenState) {
        val currentTableId = getCurrentTableId(
            selectedTables = state.selectedTables,
            selectedTableIndex = state.selectedTableIndex
        )
        if (currentTableId != null) {
            removeTableCourseStatuses(currentTableId)
            // Re-initialize the table
            onTableSelected(currentTableId)
        } else {
            // Reset global statuses
            setState {
                copy(
                    courseStatuses = emptyMap(),
                    currentActiveCourse = availableCourses.firstOrNull()?.id
                )
            }
        }
    }

    /**
     * Check if there are items in the global cart (when no table is selected)
     * @return true if there are items in the global cart
     */
    fun hasGlobalCartItems(state: ProductsScreenState): Boolean {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = state.selectedTableIndex,
            selectedTables = state.selectedTables
        )
        return if (currentTableId == null) {
            // No table selected - check global cart
            val globalCartItems = state.order.carts ?: emptyList()
            val globalCartItemsWithCourses = state.globalCartItemsWithCourses
            globalCartItems.isNotEmpty() || globalCartItemsWithCourses.isNotEmpty()
        } else {
            // Table is selected - no global cart items concern
            false
        }
    }

    /**
     * Clear global cart and course assignments (used when switching from walk-in to table mode)
     */
    fun clearGlobalCart() {
        setState {
            copy(
                order = Order(
                    carts = emptyList(),
                    netPayable = 0.0,
                    tax = 0.0,
                    totalPrice = 0.0
                ),
                globalCartItemsWithCourses = emptyList(),
                courseStatuses = emptyMap(),
                currentActiveCourse = availableCourses.firstOrNull()?.id
            )
        }
    }

    /**
     * Initialize the first course as active (with Go button)
     */
    fun initializeActiveCourse() {
        setState {
            val firstCourse = availableCourses.firstOrNull()?.id
            val currentTableId = getCurrentTableId()

            if (currentTableId != null) {
                // Initialize table-specific active course to first course
                val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
                updatedTableActiveCourses[currentTableId] = firstCourse ?: ""
                copy(tableActiveCourses = updatedTableActiveCourses)
            } else {
                // Initialize global active course to first course (for walk-in customers)
                copy(currentActiveCourse = firstCourse)
            }
        }
    }

    /**
     * Move the Go button to the next course in sequence
     * @param currentCourseId The course that was just processed
     */
    fun moveToNextCourse(currentCourseId: String) {
        setState {
            val currentTableId = getCurrentTableId()
            val currentIndex = availableCourses.indexOfFirst { it.id == currentCourseId }
            val nextCourse = if (currentIndex >= 0 && currentIndex < availableCourses.size - 1) {
                availableCourses[currentIndex + 1].id
            } else {
                // If we're at the last course, cycle back to the first course
                availableCourses.firstOrNull()?.id
            }

            if (currentTableId != null) {
                // Update table-specific active course
                val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
                updatedTableActiveCourses[currentTableId] = nextCourse ?: ""
                copy(tableActiveCourses = updatedTableActiveCourses)
            } else {
                // Update global active course (for walk-in customers)
                copy(currentActiveCourse = nextCourse)
            }
        }
    }

    /**
     * Check if a course should show the Go button
     * @param courseId The course to check
     * @return true if this course should show the Go button
     */
    fun shouldShowGoButton(
        courseId: String,
        state: ProductsScreenState
    ): Boolean {
        val currentTableId = getCurrentTableId(
            selectedTables = state.selectedTables,
            selectedTableIndex = state.selectedTableIndex
        )
        val activeCourse = if (currentTableId != null) {
            // Get table-specific active course
            state.tableActiveCourses[currentTableId] ?: state.availableCourses.firstOrNull()?.id
        } else {
            // Get global active course (for walk-in customers)
            state.currentActiveCourse ?: state.availableCourses.firstOrNull()?.id
        }
        return courseId == activeCourse && getCourseStatus(
            courseId = courseId,
            state = state
        ) == CourseStatus.GO
    }

    /**
     * Send courses notification to printer for a specific course
     * @param courseId The ID of the course to send notification for
     */
    suspend fun sendCoursesNotificationForCourse(
        courseId: String, selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        selectedTableIndex: Int,
        cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
        globalCartItemsWithCourses: List<CartItemWithCourse>,
        availableCourses: List<MealCourse>
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val flow: MutableStateFlow<Async<CoursesNotificationResponse>> = MutableStateFlow(Loading())

        try {
            val storeId = prefs.store?.id ?: -1
            val currentTableId = getCurrentTableId(
                selectedTables = selectedTables,
                selectedTableIndex = selectedTableIndex
            )
            val tableName = if (currentTableId != null) {
                if (selectedTableIndex >= 0 && selectedTableIndex < selectedTables.size) {
                    selectedTables[selectedTableIndex].tableName
                } else {
                    "Table-$currentTableId"
                }
            } else {
                "Walk-in"
            }

            // Get cart items based on whether a table is selected or not
            val courseCartItems = if (currentTableId != null) {
                // Table is selected - use table-specific cart items
                cartItemsWithCourses.filter {
                    it.value.any { item -> item.courseId == courseId }
                }.values.flatten()
            } else {
                // No table selected - use global cart items
                globalCartItemsWithCourses.filter { it.courseId == courseId }
            }

            val course = availableCourses.find { it.id == courseId }
            val courseName = course?.displayName ?: "Unknown Course"

            // Extract just the Cart objects from CartItemWithCourse
            val carts = courseCartItems.map { it.cart }

            if (carts.isNotEmpty()) {

                sendCoursesNotificationUseCase(
                    storeId = storeId,
                    courseName = courseName,
                    tableName = tableName ?: "",
                    carts = carts
                ).execute { result ->
                    when (result) {
                        is Success -> {
                            if (result()()?.success == true) {
                                // Update status to PREPARING when Go button is clicked
                                updateCourseStatus(courseId, CourseStatus.PREPARING)

                                // Handle success - move Go button to next course
                                println("Course notification sent successfully for $courseName")
                                moveToNextCourse(courseId)
                                flow.value = result()
                                // You can add logic here to automatically change to COMPLETE after some time
                                // or wait for kitchen confirmation
                            } else {
                                updateCourseStatus(courseId, CourseStatus.GO)
                                flow.value = Fail(Throwable("Unable to Print Course"))
                            }
                            copy()
                        }

                        is Fail -> {
                            // Handle error - revert status back to GO on failure
                            updateCourseStatus(courseId, CourseStatus.GO)
                            flow.value = Fail(result.error)
                            println("Failed to send course notification for $courseName: ${result.error.message}")
                            copy()
                        }

                        else -> {
                            flow.value = Uninitialized
                            // Loading state
                            println("Sending course notification for $courseName...")
                            copy()
                        }
                    }
                }
            } else {
                println("No items found for course: $courseName")
            }
        } catch (e: Exception) {
            println("Error sending course notification: ${e.message}")
        }
        return flow
    }

    suspend fun printBill(
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        selectedTableIndex: Int,
        cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
        globalCartItemsWithCourses: List<CartItemWithCourse>,
        availableCourses: List<MealCourse>
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val flow: MutableStateFlow<Async<CoursesNotificationResponse>> = MutableStateFlow(Loading())
        try {
            val storeId = prefs.store?.id ?: -1
            val currentTableId = getCurrentTableId(
                selectedTables = selectedTables,
                selectedTableIndex = selectedTableIndex
            )

            // Get cart items based on whether a table is selected or not
            val courseCartItems = if (currentTableId != null) {
                // Table is selected - use table-specific cart items
                cartItemsWithCourses.filter {
                    it.value.any { true }
                }.values.flatten()
            } else {
                // No table selected - use global cart items
                globalCartItemsWithCourses.filter { true }
            }

            // Extract just the Cart objects from CartItemWithCourse
            val carts = courseCartItems.map { it.cart }

            if (carts.isNotEmpty()) {
                printRepository.printBill(
                    request = PrintBillRequest(
                        storeId = storeId,
                        cartJson = printRepository.convertCartsToJson(
                            carts = carts
                        )
                    ),
                ).execute { result ->
                    when (result) {
                        is Success -> {
                            if (result()()?.success == true) {
                                // Handle success - move Go button to next course
                                println("Print Bill done successfully")
                                flow.value = result()
                                // You can add logic here to automatically change to COMPLETE after some time
                                // or wait for kitchen confirmation
                            } else {
                                flow.value = Fail(Throwable("Unable to Print Bill"))
                            }
                            copy()
                        }

                        is Fail -> {
                            flow.value = Fail(result.error)
                            println("Unable to Print Bill ${result.error.message}")
                            copy()
                        }

                        else -> {
                            flow.value = Uninitialized
                            copy()
                        }
                    }
                }
            } else {
                println("No items found")
            }
        } catch (e: Exception) {
            println("Error sending course notification: ${e.message}")
        }
        return flow
    }


    suspend fun sendToKitchen(
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        selectedTableIndex: Int,
        cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
        globalCartItemsWithCourses: List<CartItemWithCourse>,
        availableCourses: List<MealCourse>
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val flow: MutableStateFlow<Async<CoursesNotificationResponse>> = MutableStateFlow(Loading())
        try {
            val storeId = prefs.store?.id ?: -1
            val currentTableId = getCurrentTableId(
                selectedTables = selectedTables,
                selectedTableIndex = selectedTableIndex
            )

            // Get cart items based on whether a table is selected or not
            val courseCartItems = if (currentTableId != null) {
                // Table is selected - use table-specific cart items
                cartItemsWithCourses.filter {
                    it.value.any { true }
                }.values.flatten()
            } else {
                // No table selected - use global cart items
                globalCartItemsWithCourses.filter { true }
            }

            // Extract just the Cart objects from CartItemWithCourse
            val carts = courseCartItems.map { it.cart }

            if (carts.isNotEmpty()) {
                printRepository.sendToKitchen(
                    request = SendToKitchenRequest(
                        storeId = storeId,
                        cartJson = printRepository.convertCartsToJson(
                            carts = carts
                        ),
                        tableName = if (currentTableId != null) {
                            if (selectedTableIndex >= 0 && selectedTableIndex < selectedTables.size) {
                                selectedTables[selectedTableIndex].tableName
                            } else {
                                "Table-$currentTableId"
                            }
                        } else {
                            "Walk-in"
                        }
                    ),
                ).execute { result ->
                    when (result) {
                        is Success -> {
                            // Handle success - move Go button to next course
                            if (result()()?.success == true) {
                                // Handle success - move Go button to next course
                                println("Print Bill done successfully")
                                flow.value = result()
                                // You can add logic here to automatically change to COMPLETE after some time
                                // or wait for kitchen confirmation
                            } else {
                                flow.value = Fail(Throwable("Unable to Send to Kitchen"))
                            }
                            copy()
                        }

                        is Fail -> {
                            println("Print Bill done successfully ${result.error.message}")
                            copy()
                        }

                        else -> {
                            flow.value = Uninitialized
                            // Loading state
                            println("Print Bill done successfully")
                            copy()
                        }
                    }
                }
            } else {
                println("No items found")
            }
        } catch (e: Exception) {
            println("Error sending course notification: ${e.message}")
        }
        return flow
    }

    /**
     * Send courses notification to printer
     * This function will send notification for each course that has items in the current order
     */
    fun sendCoursesNotification(
        selectedTableIndex: Int, selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
        availableCourses: List<MealCourse>
    ) {
        viewModelScope.launch {
            try {
                val storeId = prefs.store?.id ?: -1
                val currentTableId = getCurrentTableId(
                    selectedTableIndex = selectedTableIndex,
                    selectedTables = selectedTables
                )
                val tableName = if (currentTableId != null) {
                    if (selectedTableIndex >= 0 && selectedTableIndex < selectedTables.size) {
                        selectedTables[selectedTableIndex].tableName
                    } else {
                        "Table-$currentTableId"
                    }
                } else {
                    "Walk-in"
                }

                // Group cart items by course
                val courseGroups = cartItemsWithCourses

                // Send notification for each course that has items
                courseGroups.forEach { (courseId, cartItems) ->
                    val course = availableCourses.find { it.id == courseId.toString() }
                    val courseName = course?.displayName ?: "Starters"

                    // Extract just the Cart objects from CartItemWithCourse
                    val carts = cartItems.map { it.cart }

                    if (carts.isNotEmpty()) {
                        sendCoursesNotificationUseCase(
                            storeId = storeId,
                            courseName = courseName,
                            tableName = tableName,
                            carts = carts
                        ).collect { result ->
                            when (result) {
                                is Success -> {
                                    // Handle success - maybe show a toast or update UI
                                    println("Course notification sent successfully for $courseName")
                                }

                                is Fail -> {
                                    // Handle error - maybe show error message
                                    println("Failed to send course notification for $courseName: ${result.error.message}")
                                }

                                else -> {
                                    // Loading state
                                    println("Sending course notification for $courseName...")
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                println("Error sending courses notification: ${e.message}")
            }
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<ProductsScreenViewModel, ProductsScreenState> {
        override fun create(state: ProductsScreenState): ProductsScreenViewModel
    }

    companion object :
        MavericksViewModelFactory<ProductsScreenViewModel, ProductsScreenState> by hiltMavericksViewModelFactory()
}

data class ProductsScreenState(
    val stockItemsResponse: Async<StockItemsResponse> = Uninitialized,
    val stockResponse: Async<StockItemsResponse> = Uninitialized,
    val stock: Int = 0,
    val showUpdateStockDialog: StockItem? = null,
    val isBottomSheetVisible: StockItem? = null,
    val order: Order = Order(), // Deprecated - kept for backward compatibility
    val tableOrders: Map<Int, Order> = emptyMap(), // Table-specific orders (tableId -> Order)
    val orderResponse: Async<OrderResponse2> = Uninitialized,
    val salesResponse: Async<SalesResponse> = Uninitialized,
    val optionDetailsResponse: Async<OptionDetails> = Uninitialized,
    val productTotal: Double = 0.0,
    val isShowPrintingPreview: OrderItem2? = null,
    val shouldPrintInstant: Boolean = false,
    val showCart: Boolean = false,
    val salesReportResponse: Async<SalesReportResponse> = Uninitialized,
    val showSalesReportDialog: Boolean = false,
    val salesRequest: SalesRequest? = null,
    val refreshing: Boolean = false,
    val syncStatus: SyncStatus = SyncStatus.Idle,
    val selectedTables: List<AreaTableSelectionHelper.AreaTableSelection> = emptyList(),
    val selectedTableIndex: Int = 0,
    // Course management state
    val cartItemsWithCourses: Map<Int, List<CartItemWithCourse>> = emptyMap(), // Table-specific course assignments (tableId -> List<CartItemWithCourse>)
    val selectedCourseFilter: Map<Int, CourseFilter> = emptyMap(), // Table-specific course filter (tableId -> CourseFilter)
    val globalCartItemsWithCourses: List<CartItemWithCourse> = emptyList(), // Global course assignments when no tables
    val globalSelectedCourseFilter: CourseFilter = CourseFilter.ALL, // Global course filter when no tables
    val availableCourses: List<MealCourse> = listOf(),
    val selectedCourseForNewItems: String = "", // No course selected initially
    val courseStatuses: Map<String, CourseStatus> = emptyMap(), // Global course statuses (for walk-in customers)
    val tableCourseStatuses: Map<Int, Map<String, CourseStatus>> = emptyMap(), // Table-specific course statuses (tableId -> courseId -> CourseStatus)
    val currentActiveCourse: String? = null, // Global active course (for walk-in customers)
    val tableActiveCourses: Map<Int, String> = emptyMap(), // Table-specific active courses (tableId -> courseId)
) : MavericksState {

    /**
     * Get the current table's order based on selectedTableIndex
     */
    fun getCurrentTableOrder(): Order {
        return if (selectedTables.isNotEmpty() && selectedTableIndex < selectedTables.size) {
            val currentTable = selectedTables[selectedTableIndex]
            tableOrders[currentTable.tableId] ?: Order()
        } else {
            // Fallback to global order if no tables selected
            order
        }
    }

    /**
     * Get the current table ID, or null if no table is selected
     */
    fun getCurrentTableId(): Int? {
        return if (selectedTables.isNotEmpty() && selectedTableIndex < selectedTables.size) {
            selectedTables[selectedTableIndex].tableId
        } else {
            null
        }
    }

    /**
     * Get cart items with courses for the current table
     */
    fun getCurrentTableCartItemsWithCourses(): List<CartItemWithCourse> {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            cartItemsWithCourses[currentTableId] ?: emptyList()
        } else {
            // Use global course assignments when no tables selected
            if (globalCartItemsWithCourses.isNotEmpty()) {
                globalCartItemsWithCourses
            } else {
                // Fallback to global cart items with default course assignment
                getCurrentTableOrder().carts?.map { cart ->
                    CartItemWithCourse(cart = cart, courseId = if (selectedCourseForNewItems.isNotEmpty()) selectedCourseForNewItems else availableCourses.firstOrNull()?.id ?: "")
                } ?: emptyList()
            }
        }
    }

    /**
     * Get the current course filter for the current table
     */
    fun getCurrentTableCourseFilter(): CourseFilter {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            selectedCourseFilter[currentTableId] ?: CourseFilter.ALL
        } else {
            // Use global course filter when no tables selected
            globalSelectedCourseFilter
        }
    }

    /**
     * Get filtered cart items based on current table's course filter
     */
    fun getFilteredCartItems(): List<CartItemWithCourse> {
        val cartItems = getCurrentTableCartItemsWithCourses()
        val filter = getCurrentTableCourseFilter()

        return when (filter) {
            CourseFilter.ALL -> cartItems
            CourseFilter.STARTERS -> cartItems.filter { it.courseId == "course_starters" }
            CourseFilter.MAINS -> cartItems.filter { it.courseId == "course_mains" }
            CourseFilter.DESSERTS -> cartItems.filter { it.courseId == "course_desserts" }
        }
    }

    fun isCourseSelected(courseId: String): Boolean {
        return selectedCourseForNewItems == courseId
    }

    /**
     * Get course counts for the current table
     */
    fun getCourseCounts(): Map<CourseFilter, Int> {
        val cartItems = getCurrentTableCartItemsWithCourses()
        return mapOf(
            CourseFilter.STARTERS to cartItems.count { it.courseId == "course_starters" },
            CourseFilter.MAINS to cartItems.count { it.courseId == "course_mains" },
            CourseFilter.DESSERTS to cartItems.count { it.courseId == "course_desserts" }
        )
    }
}