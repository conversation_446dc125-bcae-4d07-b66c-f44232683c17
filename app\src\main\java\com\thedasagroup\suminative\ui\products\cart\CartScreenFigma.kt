package com.thedasagroup.suminative.ui.products.cart

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.TextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCard
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Money
import androidx.compose.material.icons.filled.RemoveCircle
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.filled.Splitscreen
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.ui.theme.fontNunito
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal
import androidx.compose.ui.window.Dialog
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.TextFieldColors
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.ui.products.CATEGORY_GREEN_COLOR
import com.thedasagroup.suminative.ui.products.MealCourse
import com.thedasagroup.suminative.ui.products.CartItemWithCourse
import com.thedasagroup.suminative.ui.products.CourseFilter
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.stores.isMobilePOS
import kotlin.collections.forEach
import kotlin.collections.isNullOrEmpty
import kotlin.let
import kotlin.text.isNotEmpty
import kotlin.text.isNullOrEmpty
import kotlin.text.uppercase

@Composable
fun CartScreenFigma(
    order: Order,
    onRemoveItem: (Cart) -> Unit,
    closeCart: () -> Unit,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
    onUpdateStock: (Int, StoreItem, Cart, OptionDetails?) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onSplitBillClick: (Int) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    onAddNewCourse: (String) -> Unit,
    onCourseBillClick: (Order) -> Unit,
    onGoButtonClick: (String) -> Unit,
    onSendToKitchen: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val optionDetails by productsScreenViewModel.collectAsState(ProductsScreenState::optionDetailsResponse)
    val state by productsScreenViewModel.collectAsState()

    MobileCartScreen(
        order = order,
        onRemoveItem = onRemoveItem,
        closeCart = closeCart,
        placeOrderCash = placeOrderCash,
        placeOrderCard = placeOrderCard,
        onUpdateStock = { stock, storeItem, cart ->
            // Create OptionDetails from the cart item's store item option sets
            val cartOptionDetails =
                OptionDetails(optionSets = storeItem.optionSets ?: mutableListOf())
            onUpdateStock(stock, storeItem, cart, cartOptionDetails)
        },
        onUpdateNotes = onUpdateNotes,
        onSplitBillClick = onSplitBillClick,
        onVoidItem = onVoidItem,
        onCloudPrintClick = onCloudPrintClick,
        onAddNewCourse = onAddNewCourse,
        productsScreenViewModel = productsScreenViewModel,
        onCourseBillClick = onCourseBillClick,
        onGoButtonClick = onGoButtonClick,
        onSendToKitchen = onSendToKitchen,
        state = state
    )
}

// Enum for main cart tabs
enum class CartTab(val displayName: String) {
    ORDER("Order"),
    PAY("Pay")
}

// Enum for course status
enum class CourseStatus(val displayName: String) {
    GO("Go"),
    PREPARING("Preparing"),
    COMPLETE("Complete")
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun MobileCartScreen(
    order: Order,
    state: ProductsScreenState,
    onRemoveItem: (Cart) -> Unit,
    closeCart: () -> Unit,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onSplitBillClick: (Int) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onAddNewCourse: (String) -> Unit,
    onCourseBillClick: (Order) -> Unit,
    onGoButtonClick: (String) -> Unit,
    onSendToKitchen: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    var selectedTab by remember { mutableStateOf(CartTab.ORDER) }
    // State for edit courses dialog
    var showEditCoursesDialog by remember { mutableStateOf(false) }
    var showLastCourseAlert by remember { mutableStateOf(false) }

    // Get state from ViewModel using Mavericks
    val availableCourses = state.availableCourses
    val cartItemsWithCourses = state.getCurrentTableCartItemsWithCourses()

    // Sync course assignments when order changes
    LaunchedEffect(order.carts) {
        productsScreenViewModel.syncCartItemsWithCourses()
    }

    // Initialize the first course as active when cart opens
    LaunchedEffect(Unit) {
        productsScreenViewModel.initializeActiveCourse()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // Main tabs (Order, Bill, Pay)
        MainCartTabs(
            selectedTab = selectedTab,
            onTabSelected = { selectedTab = it },
            onBillClick = { onCourseBillClick(order) }
        )

        // Content based on selected tab
        when (selectedTab) {
            CartTab.ORDER -> {
                OrderTabContent(
                    order = order,
                    cartItemsWithCourses = cartItemsWithCourses,
                    availableCourses = availableCourses,
                    onRemoveItem = onRemoveItem,
                    onUpdateStock = onUpdateStock,
                    onUpdateNotes = onUpdateNotes,
                    onVoidItem = onVoidItem,
                    onAddNewCourse = onAddNewCourse,
                    onCourseBillClick = onCourseBillClick,
                    onGoButtonClick = onGoButtonClick,
                    onSendToKitchen = onSendToKitchen,
                    onShowEditCoursesDialog = {
                        showEditCoursesDialog = true
                    },
                    state = state,
                    productsScreenViewModel = productsScreenViewModel
                )
            }

            CartTab.PAY -> {
                PayTabContent(
                    order = order,
                    placeOrderCard = placeOrderCard,
                    placeOrderCash = placeOrderCash,
                    onSplitBillClick = onSplitBillClick,
                    onCloudPrintClick = onCloudPrintClick,
                    onVoidItem = onVoidItem,
                    productsScreenViewModel = productsScreenViewModel
                )
            }
        }
    }

    // Edit Courses Dialog
    if (showEditCoursesDialog) {
        EditCoursesDialog(
            availableCourses = availableCourses,
            onDismiss = { showEditCoursesDialog = false },
            onAddCourse = { courseName ->
                productsScreenViewModel.addNewCourse(courseName, availableCourses = availableCourses)
            },
            onEditCourse = { courseId, newName ->
                productsScreenViewModel.editCourse(courseId, newName)
            },
            onRemoveCourse = { courseId ->
                val removed = productsScreenViewModel.removeCourse(courseId, state = state)
                if (!removed) {
                    showLastCourseAlert = true
                }
            }
        )
    }

    // Last Course Alert Dialog
    if (showLastCourseAlert) {
        androidx.compose.material3.AlertDialog(
            onDismissRequest = { showLastCourseAlert = false },
            title = {
                Text(
                    text = "Cannot Remove Course",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32)
                )
            },
            text = {
                Text(
                    text = "You cannot remove the last course. At least one course must remain.",
                    color = Color.Black
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = { showLastCourseAlert = false }
                ) {
                    Text(
                        text = "OK",
                        color = Color(0xFF2E7D32),
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            containerColor = Color.White,
            titleContentColor = Color(0xFF2E7D32),
            textContentColor = Color.Black
        )
    }
}

@Composable
private fun MainCartTabs(
    selectedTab: CartTab,
    onTabSelected: (CartTab) -> Unit,
    onBillClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Order and Pay tabs
        CartTab.values().forEach { tab ->
            val isSelected = tab == selectedTab
            val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.Transparent
            val textColor = if (isSelected) Color.White else Color.Black
            val borderColor = Color(0xFF2E7D32)

            androidx.compose.material3.Card(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onTabSelected(tab) }
                    .border(
                        width = 1.dp,
                        color = borderColor,
                        shape = RoundedCornerShape(8.dp)
                    ),
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.cardColors(containerColor = backgroundColor),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Text(
                    text = tab.displayName,
                    color = textColor,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    fontFamily = fontPoppins,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp),
                    textAlign = TextAlign.Center
                )
            }
        }

        // Bill Button
        Button(
            onClick = onBillClick,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF2E7D32),
                contentColor = Color.White
            ),
            shape = RoundedCornerShape(8.dp),
            modifier = Modifier
                .weight(0.8f)
                .height(48.dp)
        ) {
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                if(!isMobilePOS) {
                    Icon(
                        imageVector = Icons.Default.Money,
                        contentDescription = "Bill",
                        modifier = Modifier.size(20.dp),
                        tint = Color.White
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                }
                Text(
                    text = "BILL",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    fontFamily = fontPoppins,
                    color = Color.White
                )
            }
        }
    }
}

@Composable
private fun OrderTabContent(
    order: Order,
    cartItemsWithCourses: List<CartItemWithCourse>,
    availableCourses: List<MealCourse>,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onAddNewCourse: (String) -> Unit = {},
    onCourseBillClick: (Order) -> Unit,
    onGoButtonClick: (String) -> Unit,
    onSendToKitchen : () -> Unit,
    onShowEditCoursesDialog: () -> Unit,
    state: ProductsScreenState,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val courseStatuses by productsScreenViewModel.collectAsState(ProductsScreenState::courseStatuses)
    var showAddCourseDialog by remember { mutableStateOf(false) }
    val selectedCourseForNewItems by productsScreenViewModel.collectAsState(ProductsScreenState::selectedCourseForNewItems)

    // Add new course dialog
    if (showAddCourseDialog) {
        AddNewCourseDialog(
            onDismiss = { showAddCourseDialog = false },
            onConfirm = { courseName ->
                showAddCourseDialog = false
                onAddNewCourse(courseName)
            }
        )
    }
    Box(modifier = Modifier.fillMaxSize()) {
        // Scrollable content
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(
                top = 16.dp,
                bottom = 200.dp
            ) // Bottom padding for sticky summary
        ) {

            item {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Course + button and name
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = {
                                showAddCourseDialog = true
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(6.dp),
                            modifier = Modifier.height(32.dp),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "Course +",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }

                        Button(
                            onClick = {
                                productsScreenViewModel.addDefaultCourses()
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(6.dp),
                            modifier = Modifier.height(32.dp),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "Add default Courses",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }

                        Button(
                            onClick = {
                              onSendToKitchen()
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(6.dp),
                            modifier = Modifier.height(32.dp),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "Send To Kitchen",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }

                        Button(
                            onClick = {
                                onShowEditCoursesDialog()
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(6.dp),
                            modifier = Modifier.height(32.dp),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "Edit Courses",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }
                    }
                }
            }
            // Show all courses, even if empty
            availableCourses.forEach { course ->
                val itemsInCourse = cartItemsWithCourses.filter { it.courseId == course.id }
                val itemCount = itemsInCourse.size
                val isSelectedCourse = selectedCourseForNewItems == course.id

                // Course header with Go button
                item {
                    CourseHeader(
                        courseName = course.displayName,
                        itemCount = itemCount,
                        status = productsScreenViewModel.getCourseStatus(course.id, state = state),
                        courseId = course.id,
                        isSelectedCourse = isSelectedCourse,
                        onAddNewCourse = { showAddCourseDialog = true },
                        onSelectCourse = { courseId ->
                            productsScreenViewModel.setSelectedCourseForNewItems(courseId)
                        },
                        onGoButtonClick = { courseId ->
                            onGoButtonClick(courseId)
                        },
                        state = state,
                        productsScreenViewModel = productsScreenViewModel
                    )
                }

                // Course items (if any)
                if (itemsInCourse.isNotEmpty()) {
                    items(
                        items = itemsInCourse,
                        key = { cartItemWithCourse ->
                            "${cartItemWithCourse.cart.storeItem?.id ?: ""}_${cartItemWithCourse.cart.quantity}_${cartItemWithCourse.courseId}_${cartItemWithCourse.cart.notes ?: ""}_${cartItemWithCourse.cart.storeItem?.optionsKey() ?: ""}"
                        }
                    ) { cartItemWithCourse ->
                        OrderItemCard(
                            cartItemWithCourse = cartItemWithCourse,
                            availableCourses = availableCourses,
                            onRemoveItem = onRemoveItem,
                            onUpdateStock = onUpdateStock,
                            onUpdateNotes = onUpdateNotes,
                            onVoidItem = onVoidItem,
                            onCourseChanged = { newCourseId ->
                                productsScreenViewModel.updateCartItemCourse(
                                    cartItemId = cartItemWithCourse.cart.storeItem?.id,
                                    newCourseId = newCourseId
                                )
                            },
                            productsScreenViewModel = productsScreenViewModel
                        )
                    }
                } else {
                    // Show empty state for this course
                    item {
                        Text(
                            text = "No items in ${course.displayName}",
                            fontSize = 14.sp,
                            color = Color.Gray,
                            fontFamily = fontPoppins,
                            modifier = Modifier.padding(vertical = 8.dp, horizontal = 16.dp)
                        )
                    }
                }
            }

            // Show overall empty cart message if no items at all
            if (cartItemsWithCourses.isEmpty()) {
                item {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        androidx.compose.material3.Icon(
                            painter = painterResource(R.drawable.cart_plus),
                            contentDescription = "Empty cart",
                            modifier = Modifier.size(80.dp),
                            tint = Color(0xFF2E7D32).copy(alpha = 0.6f)
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = "Your cart is empty",
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.Black,
                            fontFamily = fontPoppins
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "Add items to get started",
                            fontSize = 16.sp,
                            color = Color.Black.copy(alpha = 0.7f),
                            fontFamily = fontPoppins,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }

        // Sticky Order Summary at the bottom (only show if there are items)
        if (cartItemsWithCourses.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .background(color = Color.White)
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
            ) {
                androidx.compose.material3.Card(
                    modifier = Modifier
                        .background(color = Color.White)
                        .fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    shape = RoundedCornerShape(12.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Column(modifier = Modifier
                        .background(color = Color.White)
                        .padding(16.dp)) {
                        OrderSummarySection(
                            order = order,
                            onCourseBillClick = onCourseBillClick
                        )
                    }
                }
            }
        }
    }
}



@Composable
private fun CourseHeader(
    courseName: String,
    itemCount: Int,
    status: CourseStatus,
    courseId: String,
    isSelectedCourse: Boolean = false,
    onAddNewCourse: () -> Unit = {},
    onSelectCourse: (String) -> Unit = {},
    onGoButtonClick: (String) -> Unit = {},
    state: ProductsScreenState,
    productsScreenViewModel: ProductsScreenViewModel
) {
    // Course title with item count
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "$courseName ($itemCount items)",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            fontFamily = fontPoppins
        )

        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Select button
            Button(
                onClick = { onSelectCourse(courseId) },
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isSelectedCourse) Color(0xFF1B5E20) else Color(0xFF2E7D32),
                    contentColor = Color.White
                ),
                shape = RoundedCornerShape(6.dp),
                modifier = Modifier.height(32.dp),
                contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
            ) {
                Text(
                    text = if (isSelectedCourse) "Selected" else "Select",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    fontFamily = fontPoppins,
                    color = Color.White
                )
            }

            // Status-based Go button or text
            when (status) {
                CourseStatus.GO -> {
                    // Only show Go button for the active course
                    val shouldShowGo = productsScreenViewModel.shouldShowGoButton(
                        courseId = courseId,
                        state = state
                    )
                    if (shouldShowGo) {
                        Button(
                            onClick = { onGoButtonClick(courseId) },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(6.dp),
                            modifier = Modifier.height(32.dp),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "Go",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }
                    } else {
                        // Show empty space or waiting indicator for non-active courses
                        Text(
                            text = "Waiting",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            color = Color.Gray,
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                        )
                    }
                }
                CourseStatus.PREPARING -> {
                    Text(
                        text = "Preparing",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        fontFamily = fontPoppins,
                        color = Color(0xFFFF9800), // Orange color for preparing
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                    )
                }
                CourseStatus.COMPLETE -> {
                    Text(
                        text = "Complete",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        fontFamily = fontPoppins,
                        color = Color(0xFF4CAF50), // Green color for complete
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun MobileEmptyCart() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        androidx.compose.material3.Icon(
            painter = painterResource(R.drawable.cart_plus),
            contentDescription = "Empty cart",
            modifier = Modifier.size(80.dp),
            tint = Color(0xFF2E7D32).copy(alpha = 0.6f)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Your cart is empty",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            fontFamily = fontPoppins
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "Add items to get started",
            fontSize = 16.sp,
            color = Color.Black.copy(alpha = 0.7f),
            fontFamily = fontPoppins,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun OrderItemCard(
    cartItemWithCourse: CartItemWithCourse,
    availableCourses: List<MealCourse>,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onCourseChanged: (String) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val cartItem = cartItemWithCourse.cart
    var showNotesDialog by remember { mutableStateOf(false) }
    var showRemoveDialog by remember { mutableStateOf(false) }
    var itemNotes by remember { mutableStateOf(cartItem.notes ?: "") }

    if (showNotesDialog) {
        MobileOrderNotesDialog(
            initialNotes = itemNotes,
            onDismiss = { showNotesDialog = false },
            onConfirm = { notes ->
                itemNotes = notes
                val updateCartItem = cartItem.copy(notes = notes)
                onUpdateNotes(updateCartItem, notes)
                showNotesDialog = false
            }
        )
    }

    // Confirmation dialog for item removal
    if (showRemoveDialog) {
        AlertDialog(
            onDismissRequest = {
                showRemoveDialog = false
            },
            title = {
                Text(
                    text = "Remove Item",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            },
            text = {
                Text(
                    text = "Do you want to remove this item from cart?\n\n${cartItem.storeItem?.name ?: "Unknown Item"}",
                    fontSize = 16.sp,
                    fontFamily = fontPoppins
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        onRemoveItem(cartItem)
                        showRemoveDialog = false
                    }
                ) {
                    Text(
                        text = "Yes",
                        color = Color(0xFF2E7D32),
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            dismissButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        showRemoveDialog = false
                    }
                ) {
                    Text(
                        text = "No",
                        color = Color.Gray,
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            containerColor = Color.White,
            titleContentColor = Color(0xFF2E7D32),
            textContentColor = Color.Black
        )
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Item details
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // Product name row with Add Note button and delete icon
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    modifier = if(isMobilePOS) Modifier.width(130.dp) else Modifier,
                    text = cartItem.storeItem?.name ?: "",
                    fontSize = if(isMobilePOS) 12.sp else 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    fontFamily = fontPoppins,
                )

                androidx.compose.material3.IconButton(
                    onClick = { showRemoveDialog = true },
                    modifier = Modifier.size(32.dp)
                ) {
                    androidx.compose.material3.Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Delete item",
                        tint = Color.Red,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }

            // Show options one by one below the product title
            cartItem.storeItem?.optionSets?.forEach { optionSet ->
                optionSet.options?.forEach { option ->
                    if ((option?.quantity ?: 0) > 0) {
                        Text(
                            text = "Options: x${option?.quantity} - ${option?.name ?: ""}",
                            fontSize = 12.sp,
                            color = Color.Black.copy(alpha = 0.7f),
                            fontFamily = fontPoppins,
                            modifier = Modifier.padding(top = 2.dp)
                        )
                    }
                }
            }

            // Show notes section
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 2.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Notes:",
                    fontSize = 12.sp,
                    color = Color.Black.copy(alpha = 0.7f),
                    fontFamily = fontPoppins,
                    fontWeight = FontWeight.Medium
                )

                if (itemNotes.isNotEmpty()) {
                    Text(
                        text = " $itemNotes",
                        fontSize = 12.sp,
                        color = Color.Black.copy(alpha = 0.7f),
                        fontFamily = fontPoppins,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                    Spacer(modifier = Modifier.padding(start = 8.dp))
                    androidx.compose.material3.TextButton(
                        onClick = { showNotesDialog = true },
                        modifier = Modifier.height(24.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp)
                    ) {
                        Text(
                            text = "View/Edit",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            color = Color(CATEGORY_GREEN_COLOR),
                            textDecoration = TextDecoration.Underline
                        )
                    }
                } else {
                    Spacer(modifier = Modifier.padding(start = 8.dp))
                    Button(
                        onClick = { showNotesDialog = true },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(4.dp),
                        modifier = Modifier.height(24.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp)
                    ) {
                        Text(
                            text = "Add Note",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            color = Color.White
                        )
                    }
                }
            }
        }

        // Quantity controls using MobileStockUpdateCounterCart
        MobileStockUpdateCounterCart(
            initialStock = cartItem.quantity ?: 1
        ) { stock ->
            onUpdateStock(stock, cartItem.storeItem ?: StoreItem(), cartItem)
        }

        // Price
        Text(
            text = "£${cartItem.netPayable?.transformDecimal() ?: "0.00"}",
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            fontFamily = fontPoppins,
            modifier = Modifier.padding(start = 16.dp)
        )
    }
}

@Composable
private fun MobileCartItemCardWithCourse(
    cartItemWithCourse: CartItemWithCourse,
    availableCourses: List<MealCourse>,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onCourseChanged: (String) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val cartItem = cartItemWithCourse.cart
    var showNotesDialog by remember { mutableStateOf(false) }
    var showCourseDialog by remember { mutableStateOf(false) }
    var showRemoveDialog by remember { mutableStateOf(false) }
    var itemNotes by remember { mutableStateOf(cartItem.notes ?: "") }

    if (showNotesDialog) {
        MobileOrderNotesDialog(
            initialNotes = itemNotes,
            onDismiss = { showNotesDialog = false },
            onConfirm = { notes ->
                itemNotes = notes
                val updateCartItem = cartItem.copy(notes = notes)
                onUpdateNotes(updateCartItem, notes)
                showNotesDialog = false
            }
        )
    }

    if (showCourseDialog) {
        MealCourseSelectionDialog(
            currentCourseId = cartItemWithCourse.courseId,
            availableCourses = availableCourses,
            onDismiss = { showCourseDialog = false },
            onCourseSelected = { courseId ->
                onCourseChanged(courseId)
                showCourseDialog = false
            }
        )
    }

    // Confirmation dialog for item removal
    if (showRemoveDialog) {
        AlertDialog(
            onDismissRequest = {
                showRemoveDialog = false
            },
            title = {
                Text(
                    text = "Remove Item",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            },
            text = {
                Text(
                    text = "Do you want to remove this item from cart?\n\n${cartItem.storeItem?.name ?: "Unknown Item"}",
                    fontSize = 16.sp,
                    fontFamily = fontPoppins
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        onRemoveItem(cartItem)
                        showRemoveDialog = false
                    }
                ) {
                    Text(
                        text = "Yes",
                        color = Color(0xFF2E7D32),
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            dismissButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        showRemoveDialog = false
                    }
                ) {
                    Text(
                        text = "No",
                        color = Color.Gray,
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            containerColor = Color.White,
            titleContentColor = Color(0xFF2E7D32),
            textContentColor = Color.Black
        )
    }

    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Course indicator badge
            val currentCourse = availableCourses.find { it.id == cartItemWithCourse.courseId }
            androidx.compose.material3.Card(
                modifier = Modifier
                    .padding(bottom = 8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF2E7D32).copy(alpha = 0.1f)
                ),
                shape = RoundedCornerShape(6.dp)
            ) {
                Text(
                    text = currentCourse?.displayName ?: "Course 1",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }

            // Header with item name and remove button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = cartItem.storeItem?.name?.uppercase() ?: "UNKNOWN",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black,
                        fontFamily = fontPoppins
                    )

                    // Action buttons row
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        androidx.compose.material3.TextButton(
                            onClick = { showNotesDialog = true },
                            contentPadding = PaddingValues(0.dp)
                        ) {
                            Text(
                                text = if (itemNotes.isNotEmpty()) "Edit Note" else "Add Note",
                                fontSize = 14.sp,
                                color = Color(0xFF2E7D32),
                                fontFamily = fontPoppins
                            )
                        }

                        androidx.compose.material3.TextButton(
                            onClick = { showCourseDialog = true },
                            contentPadding = PaddingValues(0.dp)
                        ) {
                            val currentCourse =
                                availableCourses.find { it.id == cartItemWithCourse.courseId }
                            Text(
                                text = "Meal: ${currentCourse?.displayName ?: "Course 1"}",
                                fontSize = 14.sp,
                                color = Color(0xFF2E7D32),
                                fontFamily = fontPoppins
                            )
                        }
                    }
                }

                androidx.compose.material3.IconButton(
                    onClick = { showRemoveDialog = true },
                    modifier = Modifier.size(40.dp)
                ) {
                    androidx.compose.material3.Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Remove item",
                        tint = Color.Red
                    )
                }
                //productsScreenViewModel.prefs.storeConfigurations?.data?.quickService != true
                if (true) {
                    Spacer(modifier = Modifier.width(8.dp))
                    androidx.compose.material3.TextButton(
                        onClick = { onVoidItem(cartItem) },
                    ) {
                        Text(
                            text = "Void Item",
                            fontSize = 14.sp,
                            color = Color.Red,
                            fontFamily = fontPoppins
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Item details
            cartItem.storeItem?.let { storeItem ->
                MobileCartItemDetails(storeItem = storeItem, cartQuantity = cartItem.quantity ?: 1)
            }

            // Notes section
            if (itemNotes.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                androidx.compose.material3.Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF5F5F5)
                    )
                ) {
                    Column(modifier = Modifier.padding(8.dp)) {
                        Text(
                            text = "Notes:",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Black,
                            fontFamily = fontPoppins
                        )
                        Text(
                            text = itemNotes,
                            fontSize = 12.sp,
                            color = Color.Black.copy(alpha = 0.8f),
                            fontStyle = FontStyle.Italic,
                            fontFamily = fontPoppins,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Quantity and price section
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                MobileStockUpdateCounterCart(
                    initialStock = cartItem.quantity ?: 1
                ) { stock ->
                    onUpdateStock(stock, cartItem.storeItem ?: StoreItem(), cartItem)
                }

                Text(
                    text = "£${cartItem.netPayable?.transformDecimal() ?: "0.00"}",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            }
        }
    }
}

@Composable
private fun MobileCartItemDetails(storeItem: StoreItem, cartQuantity: Int) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Main item - use cart quantity instead of store item quantity
        MobileCartItemRow(
            quantity = cartQuantity,
            name = storeItem.name ?: "",
            price = (storeItem.price ?: 0.0) * cartQuantity
        )

        // Options
        storeItem.optionSets?.forEach { optionSet ->
            optionSet.options?.forEach { option ->
                if ((option?.quantity ?: 0) > 0) {
                    MobileCartItemRow(
                        quantity = option?.quantity ?: 0,
                        name = option?.name ?: "",
                        price = (option?.price ?: 0.0) * (option?.quantity ?: 0) * cartQuantity,
                        isExtra = true
                    )
                }
            }
        }
    }
}

@Composable
private fun MobileCartItemRow(
    quantity: Int,
    name: String,
    price: Double,
    isExtra: Boolean = false
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "${if (isExtra) "  + " else ""}$quantity x $name",
            fontSize = 14.sp,
            color = if (isExtra) Color.Black.copy(alpha = 0.7f) else Color.Black,
            fontFamily = fontPoppins,
            modifier = Modifier.weight(1f)
        )

        Text(
            text = "£${price.transformDecimal()}",
            fontSize = 14.sp,
            color = Color.Black,
            fontWeight = if (isExtra) FontWeight.Normal else FontWeight.Medium,
            fontFamily = fontPoppins
        )
    }
}

@Composable
private fun OrderSummarySection(
    order: Order,
    onCourseBillClick : (Order) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Color.White),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Sub Total
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Sub Total",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )
            Text(
                text = "£${order.netPayable?.transformDecimal() ?: "0.00"}",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )
        }

        // Service Charge
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Service Charge",
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                color = Color.Black,
                fontFamily = fontPoppins
            )
            Text(
                text = "£${order.tax?.transformDecimal() ?: "0.00"}",
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                color = Color.Black,
                fontFamily = fontPoppins
            )
        }

        // Total Payable with green background
        androidx.compose.material3.Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF2E7D32)
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Total Payable",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
                Text(
                    text = "£${order.totalPrice?.transformDecimal() ?: "0.00"}",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
            }
        }
    }
}

@Composable
private fun MobileCartSummary(order: Order) {
    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Order Summary",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2E7D32),
                fontFamily = fontPoppins
            )

            MobileSummaryRow(
                title = "Items (${order.carts?.size ?: 0})",
                value = "£${order.netPayable?.transformDecimal() ?: "0.00"}"
            )

            MobileSummaryRow(
                title = "Taxes",
                value = "£${order.tax?.transformDecimal() ?: "0.00"}"
            )

            HorizontalDivider(
                modifier = Modifier.padding(vertical = 4.dp),
                color = Color.Black.copy(alpha = 0.2f)
            )

            MobileSummaryRow(
                title = "Total Payable",
                value = "£${order.totalPrice?.transformDecimal() ?: "0.00"}",
                isBold = true,
                valueColor = Color(0xFF2E7D32)
            )
        }
    }
}

@Composable
private fun MobileSummaryRow(
    title: String,
    value: String,
    isBold: Boolean = false,
    valueColor: Color = Color.Black
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            fontSize = 14.sp,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal,
            color = Color.Black,
            fontFamily = fontPoppins
        )

        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal,
            color = valueColor,
            fontFamily = fontPoppins
        )
    }
}

@Composable
fun MobileOrderTotalSticky(
    order: Order,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
    onSplitBillClick: (Int) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val ordersResponse by productsScreenViewModel.collectAsState(ProductsScreenState::orderResponse)
    var showSplitBillDialog by remember { mutableStateOf(false) }

    if (showSplitBillDialog) {
        SplitBillDialog(
            onDismiss = { showSplitBillDialog = false },
            onConfirm = { numberOfPersons ->
                showSplitBillDialog = false
                onSplitBillClick(numberOfPersons)
            }
        )
    }

    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 48.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Total amount
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Total",
                        fontSize = 16.sp,
                        color = Color.Black.copy(alpha = 0.7f),
                        fontFamily = fontPoppins
                    )
                    Text(
                        text = "£${order.totalPrice?.transformDecimal() ?: "0.00"}",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2E7D32),
                        fontFamily = fontPoppins
                    )
                }
            }

            // Payment buttons
            if (ordersResponse is Loading) {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF2E7D32)
                    )
                }
            } else {
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {

                    //productsScreenViewModel.prefs.storeConfigurations?.data?.quickService != true
                    // Split Bill Button
                    if (true) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            androidx.compose.material3.OutlinedButton(
                                onClick = {
                                    onCloudPrintClick(order)
                                },
                                modifier = Modifier
                                    .weight(1f)
                                    .height(56.dp),
                                shape = RoundedCornerShape(12.dp),
                                border = BorderStroke(2.dp, Color(0xFF2E7D32)),
                                colors = ButtonDefaults.outlinedButtonColors(
                                    contentColor = Color(0xFF2E7D32)
                                )
                            ) {
                                androidx.compose.material3.Icon(
                                    imageVector = Icons.Default.AddCard,
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Cloud Print",
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp,
                                    fontFamily = fontPoppins
                                )
                            }

                            Button(
                                onClick = {
                                    showSplitBillDialog = true
                                },
                                modifier = Modifier
                                    .weight(1f)
                                    .height(56.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF2E7D32),
                                    contentColor = Color.White
                                ),
                                shape = RoundedCornerShape(12.dp)
                            ) {
                                androidx.compose.material3.Icon(
                                    imageVector = Icons.Default.Money,
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Split Bill Payment",
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp,
                                    fontFamily = fontPoppins,
                                    color = Color.White
                                )
                            }
//                            androidx.compose.material3.OutlinedButton(
//                                onClick = {
//                                    onCloudPrintClick(order)
//                                },
//                                modifier = Modifier
//                                    .weight(1f)
//                                    .height(56.dp),
//                                shape = RoundedCornerShape(12.dp),
//                                border = BorderStroke(2.dp, Color(0xFF2E7D32)),
//                                colors = ButtonDefaults.outlinedButtonColors(
//                                    contentColor = Color(0xFF2E7D32)
//                                )
//                            ) {
//                                androidx.compose.material3.Icon(
//                                    imageVector = Icons.Default.AddCard,
//                                    contentDescription = null,
//                                    modifier = Modifier.size(20.dp)
//                                )
//                                Spacer(modifier = Modifier.width(8.dp))
//                                Text(
//                                    text = "Cloud Print",
//                                    fontWeight = FontWeight.Bold,
//                                    fontSize = 16.sp,
//                                    fontFamily = fontPoppins
//                                )
//                            }
//                            Button(
//                                onClick = { showSplitBillDialog = true },
//                                modifier = Modifier
//                                    .fillMaxWidth()
//                                    .height(56.dp),
//                                colors = ButtonDefaults.buttonColors(
//                                    containerColor = Color(0xFF2E7D32),
//                                    contentColor = Color.White
//                                ),
//                                shape = RoundedCornerShape(12.dp)
//                            ) {
//                                androidx.compose.material3.Icon(
//                                    imageVector = Icons.Default.Splitscreen,
//                                    contentDescription = null,
//                                    modifier = Modifier.size(20.dp)
//                                )
//                                Spacer(modifier = Modifier.width(8.dp))
//                                Text(
//                                    text = "Split Bill Payment",
//                                    fontWeight = FontWeight.Bold,
//                                    fontSize = 16.sp,
//                                    fontFamily = fontPoppins,
//                                    color = Color.White
//                                )
//                            }
                        }
                    }

                    // Regular Payment Buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        androidx.compose.material3.OutlinedButton(
                            onClick = placeOrderCard,
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp),
                            shape = RoundedCornerShape(12.dp),
                            border = BorderStroke(2.dp, Color(0xFF2E7D32)),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color(0xFF2E7D32)
                            )
                        ) {
                            androidx.compose.material3.Icon(
                                imageVector = Icons.Default.AddCard,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Card",
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                fontFamily = fontPoppins
                            )
                        }

                        Button(
                            onClick = placeOrderCash,
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            androidx.compose.material3.Icon(
                                imageVector = Icons.Default.Money,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Cash",
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun MobileStockUpdateCounterCart(
    initialStock: Int = 1,
    onStockChange: (Int) -> Unit,
) {
    var stock = initialStock

    Row(
        modifier = Modifier
            .background(
                Color(0xFF2E7D32),
                RoundedCornerShape(8.dp)
            )
            .padding(4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        androidx.compose.material3.IconButton(
            onClick = {
                if (stock > 1) {
                    stock--
                    onStockChange(stock)
                }
            },
            modifier = Modifier.size(32.dp)
        ) {
            androidx.compose.material3.Icon(
                imageVector = Icons.Default.RemoveCircle,
                contentDescription = "Decrease",
                modifier = Modifier.size(20.dp),
                tint = Color.White
            )
        }

        Text(
            text = stock.toString(),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(horizontal = 8.dp),
            color = Color.White,
            fontFamily = fontPoppins
        )

        androidx.compose.material3.IconButton(
            onClick = {
                stock++
                onStockChange(stock)
            },
            modifier = Modifier.size(32.dp)
        ) {
            androidx.compose.material3.Icon(
                imageVector = Icons.Default.AddCircle,
                contentDescription = "Increase",
                modifier = Modifier.size(20.dp),
                tint = Color.White
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MobileOrderNotesDialog(
    initialNotes: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var notes by remember { mutableStateOf(initialNotes) }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Add Order Notes",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                OutlinedTextField(
                    value = notes,
                    onValueChange = { notes = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    placeholder = {
                        Text(
                            "Enter notes for this item...",
                            color = Color.Black.copy(alpha = 0.6f),
                            fontFamily = fontPoppins
                        )
                    },
                    maxLines = 5,
                    shape = RoundedCornerShape(8.dp),
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = Color(0xFF2E7D32),
                        unfocusedBorderColor = Color.Black.copy(alpha = 0.3f),
                        focusedTextColor = Color.Black,
                        unfocusedTextColor = Color.Black
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    androidx.compose.material3.TextButton(
                        onClick = onDismiss,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color.Black.copy(alpha = 0.7f)
                        )
                    ) {
                        Text(
                            "Cancel",
                            fontFamily = fontPoppins
                        )
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    androidx.compose.material3.TextButton(
                        onClick = { onConfirm(notes) },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF2E7D32)
                        )
                    ) {
                        Text(
                            "Save",
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddNewCourseDialog(
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var courseName by remember { mutableStateOf("") }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Add New Course",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                OutlinedTextField(
                    value = courseName,
                    onValueChange = { courseName = it },
                    label = {
                        Text(
                            text = "Course Name",
                            fontFamily = fontPoppins
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = Color(0xFF2E7D32),
                        focusedLabelColor = Color(0xFF2E7D32),
                        cursorColor = Color(0xFF2E7D32)
                    ),
                    singleLine = true
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    androidx.compose.material3.OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Gray
                        )
                    ) {
                        Text(
                            text = "Cancel",
                            fontFamily = fontPoppins
                        )
                    }

                    Button(
                        onClick = {
                            if (courseName.isNotBlank()) {
                                onConfirm(courseName.trim())
                            }
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White
                        ),
                        enabled = courseName.isNotBlank()
                    ) {
                        Text(
                            text = "Add Course",
                            fontFamily = fontPoppins,
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MealCourseSelectionDialog(
    currentCourseId: String,
    availableCourses: List<MealCourse>,
    onDismiss: () -> Unit,
    onCourseSelected: (String) -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Select Meal Course",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Course selection options
                availableCourses.forEach { course ->
                    val isSelected = course.id == currentCourseId
                    val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.White
                    val textColor = if (isSelected) Color.White else Color(0xFF2E7D32)
                    val borderColor = Color(0xFF2E7D32)

                    androidx.compose.material3.Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp)
                            .clickable { onCourseSelected(course.id) }
                            .border(
                                width = 2.dp,
                                color = borderColor,
                                shape = RoundedCornerShape(8.dp)
                            ),
                        shape = RoundedCornerShape(8.dp),
                        colors = CardDefaults.cardColors(containerColor = backgroundColor),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        Text(
                            text = course.displayName,
                            color = textColor,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Cancel button
                androidx.compose.material3.TextButton(
                    onClick = onDismiss,
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = Color.Black.copy(alpha = 0.7f)
                    )
                ) {
                    Text(
                        "Cancel",
                        fontFamily = fontPoppins
                    )
                }
            }
        }
    }
}

@Composable
private fun MobileCartItemCard(
    cartItem: Cart,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val state by productsScreenViewModel.collectAsState()

    // Get state from ViewModel
    val availableCourses = state.availableCourses
    val cartItemsWithCourses = state.getCurrentTableCartItemsWithCourses()

    // Find the course assignment for this cart item, default to Course 1
    val cartItemWithCourse =
        cartItemsWithCourses.find { it.cart.storeItem?.id == cartItem.storeItem?.id }
            ?: CartItemWithCourse(cart = cartItem, courseId = "course_1")

    MobileCartItemCardWithCourse(
        cartItemWithCourse = cartItemWithCourse,
        availableCourses = availableCourses,
        onRemoveItem = onRemoveItem,
        onUpdateStock = onUpdateStock,
        onUpdateNotes = onUpdateNotes,
        onVoidItem = onVoidItem,
        onCourseChanged = { newCourseId ->
            productsScreenViewModel.updateCartItemCourse(
                cartItemId = cartItem.storeItem?.id,
                newCourseId = newCourseId
            )
        },
        productsScreenViewModel = productsScreenViewModel
    )
}

@Composable
fun StockUpdateCounterCart(
    initialStock: Int = 1, onStockChange: (Int) -> Unit
) {
    var stock by remember { mutableStateOf(initialStock) }

    Row(
        modifier = Modifier
            .padding(8.dp)
            .wrapContentWidth()
            .border(
                width = 1.dp,
                color = Color(0xeFFbf0ff),
                shape = RoundedCornerShape(1.dp)
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        androidx.compose.material3.IconButton(onClick = {
            if (stock > 1) {
                stock--
                onStockChange(stock)
            }
        }) {
            androidx.compose.material3.Icon(
                modifier = Modifier
                    .background(Color.White)
                    .padding(8.dp),
                painter = painterResource(id = R.drawable.cart_minus),
                contentDescription = "Decrease"
            )
        }

        Text(
            text = stock.toString(), style = TextStyle(
                fontFamily = fontPoppins,
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp
            ), modifier = Modifier
                .background(Color(0xFFebf0ff))
                .padding(8.dp)
                .width(48.dp),
            textAlign = TextAlign.Center
        )

        androidx.compose.material3.IconButton(onClick = {
            stock++
            onStockChange(stock)
        }) {
            androidx.compose.material3.Icon(
                modifier = Modifier
                    .background(Color.White)
                    .padding(8.dp),
                painter = painterResource(id = R.drawable.cart_plus),
                contentDescription = "Decrease"
            )
        }
    }
}

@Composable
fun CartSummery(
    order: Order
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 15.dp, end = 10.dp, top = 20.dp, bottom = 20.dp),
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            TotalCartFigma(
                title = "Items (${order.carts?.size ?: 0})",
                value = "£ ${order.netPayable?.transformDecimal()}",
                isBold = false,
                style = TextStyle(
                    fontFamily = fontPoppins,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                )
            )
            Spacer(modifier = Modifier.height(12.dp))
            TotalCartFigma(
                title = "Taxes",
                value = "£ ${order.tax?.transformDecimal()}",
                isBold = false,
                style = TextStyle(
                    fontFamily = fontPoppins,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                )
            )
            Spacer(modifier = Modifier.height(12.dp))
            TotalCartFigma(
                title = "Total Payable",
                value = "£ ${order.totalPrice?.transformDecimal()}",
                isBold = true,
                style = TextStyle(
                    fontFamily = fontPoppins,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF40bfff)
                )
            )
        }
    }
}

@Composable
fun OrderTotalSticky(
    productsScreenViewModel: ProductsScreenViewModel,
    order: Order,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
) {
    val ordersResponse by productsScreenViewModel.collectAsState(ProductsScreenState::orderResponse)
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 15.dp, end = 10.dp, top = 20.dp, bottom = 20.dp),
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            TotalCartFigma(
                title = "Total Payable",
                value = "£ ${order.totalPrice?.transformDecimal()}",
                isBold = true,
                style = TextStyle(
                    fontFamily = fontPoppins,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF40bfff)
                )
            )
            Spacer(modifier = Modifier.height(5.dp))
            Text(
                modifier = Modifier
                    .padding(start = 2.dp)
                    .width(170.dp),
                text = "Summary",
                style = TextStyle(
                    fontFamily = fontPoppins,
                    fontWeight = FontWeight.Normal,
                    fontSize = 10.sp,
                    fontStyle = FontStyle.Normal,
                    textDecoration = TextDecoration.Underline
                )
            )
            Spacer(modifier = Modifier.height(5.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (ordersResponse is Loading) {
                    CircularProgressIndicator(color = Color.Blue)
                } else {
                    TextButton(
                        onClick = {
                            placeOrderCard()
                        },
                        modifier = Modifier
                            .background(color = Color.Green)
                            .padding(5.dp),
                        shape = CircleShape
                    ) {
                        Text(
                            text = "Card", color = Color.Black,
                            style = TextStyle(
                                fontFamily = fontPoppins,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold
                            )
                        )
                    }
                    Spacer(modifier = Modifier.width(20.dp))
                    TextButton(
                        onClick = {
                            placeOrderCash()
                        },
                        modifier = Modifier
                            .background(color = Color.Red)
                            .padding(5.dp),
                        shape = CircleShape
                    ) {
                        Text(
                            text = "Cash", color = Color.White,
                            style = TextStyle(
                                fontFamily = fontPoppins,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold
                            )
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun CartItemCard(
    cartItem: Cart,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit
) {
    var showNotesDialog by remember { mutableStateOf(false) }
    var showRemoveDialog by remember { mutableStateOf(false) }
    var itemNotes by remember { mutableStateOf(cartItem.notes ?: "") }

    if (showNotesDialog) {
        OrderNotesDialog(
            initialNotes = itemNotes,
            onDismiss = { showNotesDialog = false },
            onConfirm = { notes ->
                itemNotes = notes
                val updateCartItem = cartItem.copy(notes = notes)
                onUpdateNotes(updateCartItem, notes)
                showNotesDialog = false
            }
        )
    }

    // Confirmation dialog for item removal
    if (showRemoveDialog) {
        AlertDialog(
            onDismissRequest = {
                showRemoveDialog = false
            },
            title = {
                Text(
                    text = "Remove Item",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            },
            text = {
                Text(
                    text = "Do you want to remove this item from cart?\n\n${cartItem.storeItem?.name ?: "Unknown Item"}",
                    fontSize = 16.sp,
                    fontFamily = fontPoppins
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        onRemoveItem(cartItem)
                        showRemoveDialog = false
                    }
                ) {
                    Text(
                        text = "Yes",
                        color = Color(0xFF2E7D32),
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            dismissButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        showRemoveDialog = false
                    }
                ) {
                    Text(
                        text = "No",
                        color = Color.Gray,
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            containerColor = Color.White,
            titleContentColor = Color(0xFF2E7D32),
            textContentColor = Color.Black
        )
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 15.dp, end = 10.dp, top = 20.dp, bottom = 20.dp),
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = 10.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier) {
                    Text(
                        text = cartItem.storeItem?.name ?: "",
                        fontFamily = fontNunito,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )
                    Spacer(modifier = Modifier.padding(5.dp))
                    TextButton(
                        onClick = { showNotesDialog = true },
                        colors = androidx.compose.material.ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF3F51B5)
                        )
                    ) {
                        Text(
                            "Add Note",
                        )
                    }
                }
                IconButton(
                    modifier = Modifier.width(24.dp),
                    onClick = {
                        showRemoveDialog = true
                    },
                ) {
                    Icon(
                        painter = painterResource(R.drawable.trash),
                        contentDescription = "Remove",
                        modifier = Modifier.size(24.dp),
                    )
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
            cartItem.storeItem?.let { storeItem ->
                TotalCartFigma(
                    title = "☐ ${storeItem?.quantity} x ${storeItem?.name}",
                    value = "£ ${((storeItem?.price ?: 0.0) * (storeItem?.quantity ?: 0)).transformDecimal()}",
                    isBold = true
                )
                Spacer(modifier = Modifier.height(16.dp))
                storeItem?.extras?.forEach { element ->
                    TotalCartFigma(
                        title = "☐ ${element.quantity} x ${element.name}",
                        value = "£ ${((storeItem.price ?: 0.0) * (element.quantity ?: 0)).transformDecimal()}",
                    )
                    Spacer(modifier = Modifier.padding(4.dp))
                }
                storeItem?.optionSets?.forEach { element ->
                    element.options?.forEach { option ->
                        if ((option?.quantity ?: 0) > 0) {
                            TotalCartFigma(
                                title = "☐ ${option?.quantity} x ${option?.name}",
                                value = "£ ${((option?.price ?: 0.0) * (option?.quantity ?: 0) * (storeItem.quantity ?: 0)).transformDecimal()}"
                            )
                            Spacer(modifier = Modifier.padding(4.dp))
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
            // Display notes if they exist
            if (!itemNotes.isNullOrEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    backgroundColor = Color(0xFFF5F5F5)
                ) {
                    Column(modifier = Modifier.padding(8.dp)) {
                        Text(
                            text = "Notes:",
                            style = TextStyle(
                                fontFamily = fontPoppins,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold
                            )
                        )
                        Text(
                            text = itemNotes,
                            style = TextStyle(
                                fontFamily = fontPoppins,
                                fontSize = 12.sp,
                                fontStyle = FontStyle.Italic
                            ),
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = 10.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                StockUpdateCounterCart { st ->
                    onUpdateStock(st, cartItem.storeItem ?: StoreItem(), cartItem)
                }
                Text(
                    modifier = Modifier.width(70.dp),
                    text = "£ ${cartItem.netPayable?.transformDecimal() ?: ""}",
                    style = TextStyle(
                        fontFamily = fontPoppins,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF40bfff)
                    ),
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
fun OrderNotesDialog(
    initialNotes: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var notes by remember { mutableStateOf(initialNotes) }

    Dialog(onDismissRequest = {
        onDismiss()
    }) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Add Order Notes",
                    style = TextStyle(
                        fontFamily = fontPoppins,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    ),
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                TextField(
                    value = notes,
                    onValueChange = { notes = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    placeholder = { Text("Enter notes for this item...") },
                    maxLines = 5
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    TextButton(
                        onClick = { onConfirm(notes) },
                        colors = androidx.compose.material.ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF40bfff)
                        )
                    ) {
                        Text("Save")
                    }
                }
            }
        }
    }
}

@Composable
fun TotalCartFigma(
    title: String, value: String, style: TextStyle = TextStyle(
        fontFamily = fontNunito,
        fontSize = 14.sp,
        fontWeight = FontWeight.Normal,
    ), isBold: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        androidx.compose.material3.Text(
            modifier = Modifier.width(180.dp),
            text = title,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
        androidx.compose.material3.Text(
            modifier = Modifier.width(80.dp),
            text = value,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SplitBillDialog(
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit
) {
    var numberOfPersons by remember { mutableStateOf("") }
    var isError by remember { mutableStateOf(false) }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Split Bill Payment",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )

                Text(
                    text = "Enter the number of persons to split the bill",
                    fontSize = 16.sp,
                    color = Color.Black.copy(alpha = 0.7f),
                    fontFamily = fontPoppins,
                    textAlign = TextAlign.Center
                )

                OutlinedTextField(
                    value = numberOfPersons,
                    onValueChange = { newValue ->
                        // Only allow numeric input
                        if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.toIntOrNull() != null)) {
                            numberOfPersons = newValue
                            // Validate the number
                            val number = newValue.toIntOrNull()
                            isError = number != null && (number < 1 || number > 20)
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    label = {
                        Text(
                            "Number of Persons",
                            fontFamily = fontPoppins
                        )
                    },
                    placeholder = {
                        Text(
                            "Enter number (1-20)",
                            color = Color.Black.copy(alpha = 0.6f),
                            fontFamily = fontPoppins
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    isError = isError,
                    supportingText = if (isError) {
                        {
                            Text(
                                "Number must be between 1 and 20",
                                color = Color.Red,
                                fontFamily = fontPoppins
                            )
                        }
                    } else null,
                    shape = RoundedCornerShape(8.dp),
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = Color(0xFF2E7D32),
                        unfocusedBorderColor = Color.Black.copy(alpha = 0.3f),
                        focusedTextColor = Color.Black,
                        unfocusedTextColor = Color.Black,
                        focusedLabelColor = Color(0xFF2E7D32),
                        unfocusedLabelColor = Color.Black.copy(alpha = 0.6f)
                    )
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    androidx.compose.material3.OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(8.dp),
                        border = BorderStroke(1.dp, Color.Black.copy(alpha = 0.3f)),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Black.copy(alpha = 0.7f)
                        )
                    ) {
                        Text(
                            "Cancel",
                            fontFamily = fontPoppins,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Button(
                        onClick = {
                            val number = numberOfPersons.toIntOrNull()
                            if (number != null && number in 1..20) {
                                onConfirm(number)
                            }
                        },
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        enabled = numberOfPersons.isNotEmpty() && !isError && numberOfPersons.toIntOrNull() != null,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White,
                            disabledContainerColor = Color.Gray.copy(alpha = 0.3f),
                            disabledContentColor = Color.Gray
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            "Open Split Bill Screen",
                            fontWeight = FontWeight.Bold,
                            fontFamily = fontPoppins
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditCoursesDialog(
    availableCourses: List<MealCourse>,
    onDismiss: () -> Unit,
    onAddCourse: (String) -> Unit,
    onEditCourse: (String, String) -> Unit,
    onRemoveCourse: (String) -> Unit
) {
    var showAddCourseDialog by remember { mutableStateOf(false) }
    var editingCourse by remember { mutableStateOf<MealCourse?>(null) }
    var editCourseName by remember { mutableStateOf("") }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                // Title
                Text(
                    text = "Edit Courses",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Course list
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(availableCourses) { course ->
                        CourseEditItem(
                            course = course,
                            onEdit = {
                                editingCourse = course
                                editCourseName = course.displayName
                            },
                            onRemove = { onRemoveCourse(course.id) }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    androidx.compose.material3.TextButton(
                        onClick = { showAddCourseDialog = true },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Add Course",
                            color = Color(0xFF2E7D32),
                            fontWeight = FontWeight.Bold
                        )
                    }

                    androidx.compose.material3.TextButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Done",
                            color = Color(0xFF2E7D32),
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }

    // Add Course Dialog
    if (showAddCourseDialog) {
        AddNewCourseDialog(
            onDismiss = { showAddCourseDialog = false },
            onConfirm = { courseName ->
                onAddCourse(courseName)
                showAddCourseDialog = false
            }
        )
    }

    // Edit Course Dialog
    editingCourse?.let { course ->
        EditCourseNameDialog(
            currentName = editCourseName,
            onDismiss = {
                editingCourse = null
                editCourseName = ""
            },
            onConfirm = { newName ->
                onEditCourse(course.id, newName)
                editingCourse = null
                editCourseName = ""
            },
            onNameChange = { editCourseName = it }
        )
    }
}

@Composable
private fun CourseEditItem(
    course: MealCourse,
    onEdit: () -> Unit,
    onRemove: () -> Unit
) {
    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFF2E7D32).copy(alpha = 0.1f)),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = course.displayName,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black,
                fontFamily = fontPoppins,
                modifier = Modifier.weight(1f)
            )

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                IconButton(
                    onClick = onEdit,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Edit Course",
                        tint = Color(0xFF2E7D32),
                        modifier = Modifier.size(18.dp)
                    )
                }

                IconButton(
                    onClick = onRemove,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Remove Course",
                        tint = Color(0xFFD32F2F),
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditCourseNameDialog(
    currentName: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit,
    onNameChange: (String) -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Text(
                    text = "Edit Course Name",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                OutlinedTextField(
                    value = currentName,
                    onValueChange = onNameChange,
                    label = { Text("Course Name") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    androidx.compose.material3.TextButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Cancel",
                            color = Color.Gray,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    androidx.compose.material3.TextButton(
                        onClick = {
                            if (currentName.isNotBlank()) {
                                onConfirm(currentName.trim())
                            }
                        },
                        modifier = Modifier.weight(1f),
                        enabled = currentName.isNotBlank()
                    ) {
                        Text(
                            text = "Save",
                            color = if (currentName.isNotBlank()) Color(0xFF2E7D32) else Color.Gray,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}
