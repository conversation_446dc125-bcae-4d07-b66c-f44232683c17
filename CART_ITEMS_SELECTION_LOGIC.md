# Cart Items Selection Logic Implementation

## Overview
This document explains the implementation of conditional cart items selection in the `sendCoursesNotificationForCourse` function, where the system uses different cart item collections based on whether a table is selected or not.

## Problem Statement
The POS system supports two modes of operation:
1. **Table Mode**: When a specific table is selected, cart items are organized per table
2. **Walk-in Mode**: When no table is selected, cart items are stored globally

The `sendCoursesNotificationForCourse` function needed to be updated to use the appropriate cart items collection based on the current mode.

## Implementation

### Cart Items Selection Logic

```kotlin
// Get cart items based on whether a table is selected or not
val courseCartItems = if (currentTableId != null) {
    // Table is selected - use table-specific cart items
    cartItemsWithCourses.filter {
        it.value.any { item -> item.courseId == courseId }
    }.values.flatten()
} else {
    // No table selected - use global cart items
    withState { globalCartItemsWithCourses.filter { it.courseId == courseId } }
}
```

### Data Structures

#### Table-Specific Cart Items
```kotlin
val cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>
// Structure: Map<TableId, List<CartItemWithCourse>>
// Example: {
//   101 -> [CartItemWithCourse(cart=Cart(...), courseId="course_starters")],
//   102 -> [CartItemWithCourse(cart=Cart(...), courseId="course_mains")]
// }
```

#### Global Cart Items
```kotlin
val globalCartItemsWithCourses: List<CartItemWithCourse>
// Structure: List<CartItemWithCourse>
// Example: [
//   CartItemWithCourse(cart=Cart(...), courseId="course_starters"),
//   CartItemWithCourse(cart=Cart(...), courseId="course_mains")
// ]
```

## Function Variations

### 1. Convenience Method (Recommended)
```kotlin
fun sendCoursesNotificationForCourse(courseId: String)
```
- **Usage**: Simple call with just course ID
- **Behavior**: Automatically gets current state and calls the full method
- **Best For**: UI components, simple use cases

### 2. Full Method
```kotlin
fun sendCoursesNotificationForCourse(
    courseId: String,
    selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
    selectedTableIndex: Int,
    cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
    availableCourses: List<MealCourse>
)
```
- **Usage**: When you have specific state parameters
- **Behavior**: Uses provided parameters for cart items selection
- **Best For**: Testing, specific state scenarios

## Selection Logic Flow

### Table Mode (currentTableId != null)
1. **Check**: `currentTableId` is not null
2. **Source**: Use `cartItemsWithCourses` (table-specific)
3. **Filter**: Find tables that have items for the specified course
4. **Extract**: Flatten all matching cart items from all tables
5. **Result**: Cart items from table-specific storage

### Walk-in Mode (currentTableId == null)
1. **Check**: `currentTableId` is null
2. **Source**: Use `globalCartItemsWithCourses` (global)
3. **Filter**: Find items that match the specified course
4. **Result**: Cart items from global storage

## Benefits

### 1. Mode-Aware Operation
- **Table Mode**: Sends notifications for items assigned to specific tables
- **Walk-in Mode**: Sends notifications for globally stored items
- **Automatic**: No manual mode switching required

### 2. Data Consistency
- **Correct Source**: Always uses the appropriate data source
- **No Conflicts**: Prevents mixing table and global data
- **State Alignment**: Matches the current operational mode

### 3. Flexibility
- **Dual Support**: Works in both table and walk-in modes
- **Seamless Transition**: Automatically adapts to mode changes
- **Backward Compatible**: Maintains existing functionality

## Usage Examples

### From UI Components
```kotlin
// Simple usage - automatically detects mode
productsScreenViewModel.sendCoursesNotificationForCourse("course_starters")
```

### From Tests or Specific Scenarios
```kotlin
// Explicit usage with specific state
viewModel.sendCoursesNotificationForCourse(
    courseId = "course_mains",
    selectedTables = tablesList,
    selectedTableIndex = 0,
    cartItemsWithCourses = tableCartItems,
    availableCourses = coursesList
)
```

## Testing

### Test Scenarios

#### 1. Table Mode Test
```kotlin
@Test
fun `test table mode uses table-specific cart items`() {
    // Given: Table is selected and has cart items
    val tableId = 101
    val tableCartItems = mapOf(tableId to listOf(cartItem))
    
    // When: sendCoursesNotificationForCourse is called
    // Then: Should use tableCartItems, not globalCartItems
}
```

#### 2. Walk-in Mode Test
```kotlin
@Test
fun `test walk-in mode uses global cart items`() {
    // Given: No table selected, global cart items exist
    val globalCartItems = listOf(cartItem)
    
    // When: sendCoursesNotificationForCourse is called
    // Then: Should use globalCartItems, not tableCartItems
}
```

#### 3. Course Filtering Test
```kotlin
@Test
fun `test course filtering works in both modes`() {
    // Given: Items for multiple courses in both table and global storage
    // When: Request specific course notification
    // Then: Should only include items for that course
}
```

## Error Handling

### Edge Cases
1. **Empty Cart Items**: Function handles empty collections gracefully
2. **Invalid Course ID**: Returns empty list if course not found
3. **Null Table ID**: Correctly falls back to global cart items
4. **Missing State**: Uses withState to safely access current state

### Validation
```kotlin
if (carts.isNotEmpty()) {
    // Proceed with notification
} else {
    println("No items found for course: $courseName")
}
```

## Performance Considerations

### Efficient Filtering
- **Table Mode**: Filters map entries, then flattens
- **Walk-in Mode**: Direct list filtering
- **Lazy Evaluation**: Only processes items for the requested course

### Memory Usage
- **No Duplication**: Uses existing data structures
- **Minimal Allocation**: Reuses filtered collections
- **State Access**: Uses withState for safe state access

## Future Enhancements

### Potential Improvements
1. **Caching**: Cache filtered results for repeated calls
2. **Batch Processing**: Support multiple courses in one call
3. **Validation**: Add validation for data consistency
4. **Metrics**: Track usage patterns between modes
5. **Optimization**: Optimize filtering for large datasets

## Files Modified

### Core Implementation
- `ProductsScreenViewModel.kt` - Updated cart items selection logic
- `CourseStatusTest.kt` - Added tests for selection logic

### Key Changes
1. **Conditional Logic**: Added table vs global cart items selection
2. **Convenience Method**: Added simple overload for UI usage
3. **State Safety**: Used withState for safe state access
4. **Documentation**: Added comprehensive comments

The implementation ensures that course notifications always use the correct cart items based on the current operational mode, providing consistent behavior across table and walk-in scenarios.
