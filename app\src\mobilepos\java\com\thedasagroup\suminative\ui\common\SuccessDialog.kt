package com.thedasagroup.suminative.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import kotlinx.coroutines.delay

@Composable
fun SuccessDialog(
    message: String = "Order Placed Successfully",
    isVisible: Boolean = false,
    onDismiss: () -> Unit = {},
    autoDismissDelayMs: Long = 3000L
) {
    if (isVisible) {
        Popup(
            onDismissRequest = {onDismiss()},
            properties = PopupProperties(
                focusable = true,
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                excludeFromSystemGesture = true,
            )
        ) {
            Box {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.TopCenter)
                        .padding(16.dp),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 8.dp
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(20.dp),
                        verticalAlignment = Alignment.Top
                    ) {
                        // Icon and Success Column
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // Success Icon
                            Box(
                                modifier = Modifier
                                    .size(32.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFF4CAF50)),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = "Success",
                                    tint = Color.White,
                                    modifier = Modifier.size(20.dp)
                                )
                            }

                            Spacer(modifier = Modifier.height(2.dp))

                            // Success Text
                            Text(
                                text = "Success",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF4CAF50)
                            )
                        }

                        Spacer(modifier = Modifier.width(16.dp))

                        // Messages Column
                        Column(
                            modifier = Modifier.weight(1f)
                                .align(Alignment.CenterVertically)
                        ) {
                            // Message Success Message
                            Text(
                                text = message,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Normal,
                                color = Color.Black
                            )
                        }
                    }
                }
            }
        }

        // Auto dismiss after delay
        LaunchedEffect(isVisible) {
            if (isVisible) {
                delay(autoDismissDelayMs)
                onDismiss()
            }
        }
    }
}

@Composable
fun FailureDialog(
    message: String = "Payment Failed",
    isVisible: Boolean = false,
    onDismiss: () -> Unit = {},
    autoDismissDelayMs: Long = 3000L
) {
    if (isVisible) {
        Box {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 8.dp
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(20.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    // Icon and Failed Column
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // Failure Icon
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .background(Color(0xFFE53E3E)),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Failed",
                                tint = Color.White,
                                modifier = Modifier.size(20.dp)
                            )
                        }

                        Spacer(modifier = Modifier.height(2.dp))

                        // Failed Text
                        Text(
                            text = "Failed",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFFE53E3E)
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    // Messages Column
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        // Message Text
                        Text(
                            text = message,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Normal,
                            color = Color.Black
                        )
                    }
                }
            }
        }

        // Auto dismiss after delay
        LaunchedEffect(isVisible) {
            if (isVisible) {
                delay(autoDismissDelayMs)
                onDismiss()
            }
        }
    }
}

@Composable
fun CancelledDialog(
    message: String = "Payment Cancelled",
    isVisible: Boolean = false,
    onDismiss: () -> Unit = {},
    autoDismissDelayMs: Long = 2000L
) {
    if (isVisible) {
        Box() {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 8.dp
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(20.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    // Icon and Cancelled Column
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // Cancelled Icon
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .background(Color(0xFFFF9800)),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Warning,
                                contentDescription = "Cancelled",
                                tint = Color.White,
                                modifier = Modifier.size(20.dp)
                            )
                        }

                        Spacer(modifier = Modifier.height(2.dp))

                        // Cancelled Text
                        Text(
                            text = "Cancelled",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFFFF9800)
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    // Messages Column
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        // Message Text
                        Text(
                            text = message,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Normal,
                            color = Color.Black
                        )
                    }
                }
            }
        }

        // Auto dismiss after delay
        LaunchedEffect(isVisible) {
            if (isVisible) {
                delay(autoDismissDelayMs)
                onDismiss()
            }
        }
    }
} 