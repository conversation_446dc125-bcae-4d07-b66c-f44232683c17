# Cart Clear Alert Implementation

## Overview
This document outlines the implementation of a cart clearing alert that warns users when selecting a table while having items in the global cart (walk-in mode). The system prevents accidental loss of cart items by requiring user confirmation before clearing.

## Problem Statement
Previously, when users had items in their global cart (walk-in mode) and selected a table, the cart items would be lost without warning. This could lead to:
- Accidental loss of customer orders
- Confusion about missing cart items
- Poor user experience
- Potential revenue loss

## Solution
Implemented a confirmation dialog that:
- Detects when user has global cart items and no table selected
- Shows an alert before clearing cart when selecting a table
- Allows user to cancel or confirm the cart clearing action
- Provides clear messaging about what will happen

## Implementation Details

### 1. Helper Functions in ProductsScreenViewModel

#### Check for Global Cart Items
```kotlin
fun hasGlobalCartItems(): Boolean {
    return withState { state ->
        val currentTableId = getCurrentTableId()
        if (currentTableId == null) {
            // No table selected - check global cart
            val globalCartItems = state.order.carts ?: emptyList()
            val globalCartItemsWithCourses = state.globalCartItemsWithCourses
            globalCartItems.isNotEmpty() || globalCartItemsWithCourses.isNotEmpty()
        } else {
            // Table is selected - no global cart items concern
            false
        }
    }
}
```

#### Clear Global Cart
```kotlin
fun clearGlobalCart() {
    setState {
        copy(
            order = Order(
                carts = emptyList(),
                netPayable = 0.0,
                tax = 0.0,
                totalPrice = 0.0
            ),
            globalCartItemsWithCourses = emptyList(),
            courseStatuses = emptyMap(),
            currentActiveCourse = availableCourses.firstOrNull()?.id
        )
    }
}
```

### 2. UI State Management in ProductsScreen

#### Dialog State Variables
```kotlin
// State for cart clear confirmation dialog
var showCartClearDialog by remember { mutableStateOf(false) }
var pendingTableSelection by remember { mutableStateOf<AreaTableSelectionHelper.AreaTableSelection?>(null) }
```

#### Table Selection Handler
```kotlin
val handleTableSelection = { selection: AreaTableSelectionHelper.AreaTableSelection ->
    if (selectedTables.isEmpty() && viewModel.hasGlobalCartItems()) {
        // Show confirmation dialog if no tables selected and global cart has items
        pendingTableSelection = selection
        showCartClearDialog = true
    } else {
        // Directly add table if no global cart items or tables already selected
        viewModel.addSelectedTable(selection)
        orderScreenViewModel.updateCurrentRoute("0")
    }
}
```

### 3. Alert Dialog Implementation

#### Dialog Structure
```kotlin
if (showCartClearDialog && pendingTableSelection != null) {
    AlertDialog(
        onDismissRequest = { /* Cancel action */ },
        title = { Text("Clear Cart") },
        text = { 
            Text("You have items in your cart. Selecting a table will clear all current cart items. Do you want to continue?") 
        },
        confirmButton = { 
            TextButton(onClick = { 
                // Clear cart and add table
                viewModel.clearGlobalCart()
                viewModel.addSelectedTable(selection)
                orderScreenViewModel.updateCurrentRoute("0")
            }) {
                Text("Yes, Clear Cart")
            }
        },
        dismissButton = { 
            TextButton(onClick = { /* Cancel */ }) {
                Text("Cancel")
            }
        }
    )
}
```

## User Flow

### Scenario 1: No Global Cart Items
```
User has empty cart → Selects table → Table added directly (no alert)
```

### Scenario 2: Global Cart Items Present
```
User has items in global cart → Selects table → Alert shown → User chooses:
├── "Yes, Clear Cart" → Cart cleared, table added, navigate to Store Items
└── "Cancel" → Dialog dismissed, no changes made
```

### Scenario 3: Tables Already Selected
```
User already has tables → Selects another table → Table added directly (no alert)
```

## Benefits

### 1. Prevents Data Loss
- ✅ Users can't accidentally lose cart items
- ✅ Clear warning before destructive action
- ✅ Option to cancel and preserve cart

### 2. Improved UX
- ✅ Clear messaging about what will happen
- ✅ Consistent with user expectations
- ✅ Reduces confusion and support requests

### 3. Business Protection
- ✅ Prevents accidental order loss
- ✅ Reduces potential revenue loss
- ✅ Improves staff confidence in system

## Technical Features

### 1. Smart Detection
- **Context Aware**: Only shows alert when relevant (no tables + global cart items)
- **Efficient**: Minimal performance impact
- **Accurate**: Checks both cart types (order.carts and globalCartItemsWithCourses)

### 2. Complete Cleanup
- **Order Data**: Clears carts, totals, taxes
- **Course Assignments**: Clears globalCartItemsWithCourses
- **Status Reset**: Resets course statuses and active course
- **State Consistency**: Ensures clean state transition

### 3. User-Friendly Dialog
- **Clear Title**: "Clear Cart" - immediately understandable
- **Descriptive Text**: Explains exactly what will happen
- **Action Buttons**: "Yes, Clear Cart" (destructive) vs "Cancel" (safe)
- **Visual Design**: Consistent with app theme

## Edge Cases Handled

### 1. Empty Cart States
- **No Items**: No alert shown, table added directly
- **Only Course Assignments**: Alert shown if globalCartItemsWithCourses has items
- **Mixed States**: Alert shown if either cart type has items

### 2. Table States
- **No Tables**: Alert logic active
- **Existing Tables**: Alert bypassed (table mode already active)
- **Table Switching**: No alert (already in table mode)

### 3. Navigation
- **Successful Addition**: Navigates to Store Items tab
- **Cancellation**: Stays on current screen
- **State Preservation**: Maintains current state on cancel

## Testing

### Test Coverage

#### 1. Global Cart Detection
```kotlin
@Test
fun `test hasGlobalCartItems returns true when global cart has items`()
@Test
fun `test hasGlobalCartItems returns false when table is selected`()
```

#### 2. Cart Clearing
```kotlin
@Test
fun `test clearGlobalCart clears all global data`()
```

#### 3. UI Flow Testing
- Manual testing of dialog appearance
- Confirmation button functionality
- Cancel button functionality
- State preservation on cancel

### Test Scenarios
1. **Empty Cart + Table Selection**: No alert, direct addition
2. **Items in Cart + Table Selection**: Alert shown, user can choose
3. **Existing Tables + New Table**: No alert, direct addition
4. **Dialog Cancel**: No changes, dialog dismissed
5. **Dialog Confirm**: Cart cleared, table added, navigation occurs

## Files Modified

### Core Implementation
- `ProductsScreenViewModel.kt` - Added helper functions
- `ProductsScreen.kt` - Added dialog and selection logic
- `CourseStatusTest.kt` - Added comprehensive tests

### Key Changes
1. **Helper Functions**: `hasGlobalCartItems()`, `clearGlobalCart()`
2. **UI State**: Added dialog state variables
3. **Selection Logic**: Added conditional table selection handler
4. **Alert Dialog**: Complete dialog implementation with proper styling

## Future Enhancements

### Potential Improvements
1. **Save Cart Option**: Allow saving cart before clearing
2. **Transfer Items**: Option to transfer items to selected table
3. **Undo Functionality**: Allow undoing cart clear action
4. **Customizable Messages**: Admin-configurable alert messages
5. **Analytics**: Track how often users encounter this scenario

## Configuration Options

### Customizable Elements
- **Dialog Title**: Currently "Clear Cart"
- **Dialog Message**: Currently explains the action
- **Button Text**: "Yes, Clear Cart" and "Cancel"
- **Colors**: Uses app theme colors (green primary)

### Behavioral Settings
- **Auto-Clear**: Could add option to auto-clear without confirmation
- **Remember Choice**: Could remember user preference
- **Timeout**: Could add auto-dismiss after timeout

The implementation provides a robust, user-friendly solution that prevents accidental cart loss while maintaining a smooth user experience for legitimate table selection scenarios.
