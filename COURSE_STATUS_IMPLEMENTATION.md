# Course Status Implementation

## Overview
This document outlines the implementation of the three-status system for course management in the POS system. The system allows tracking course preparation status with visual indicators and automated notifications.

## Course Status System

### Status Types
The system implements three distinct statuses for each course:

1. **GO** - Initial status, shows a clickable "Go" button
2. **PREPARING** - Shows "Preparing" text in orange color
3. **COMPLETE** - Shows "Complete" text in green color

### Status Flow
```
GO → PREPARING → COMPLETE
 ↑                   ↓
 ←←←← (Reset) ←←←←←←←←
```

## Implementation Details

### 1. CourseStatus Enum
**File**: `app/src/main/java/com/thedasagroup/suminative/ui/products/cart/CartScreenFigma.kt`

```kotlin
enum class CourseStatus(val displayName: String) {
    GO("Go"),
    PREPARING("Preparing"),
    COMPLETE("Complete")
}
```

### 2. State Management
**File**: `app/src/main/java/com/thedasagroup/suminative/ui/products/ProductsScreenViewModel.kt`

Added to `ProductsScreenState`:
```kotlin
val courseStatuses: Map<String, CourseStatus> = emptyMap()
```

### 3. ViewModel Functions

#### Core Status Management
- `updateCourseStatus(courseId: String, status: CourseStatus)` - Updates course status
- `getCourseStatus(courseId: String): CourseStatus` - Gets current status (defaults to GO)
- `markCourseAsComplete(courseId: String)` - Convenience method to mark complete
- `resetCourseStatus(courseId: String)` - Resets status back to GO

#### Course-Specific Notification
- `sendCoursesNotificationForCourse(courseId: String)` - Sends notification for specific course only

### 4. UI Implementation

#### CourseHeader Component
**File**: `app/src/main/java/com/thedasagroup/suminative/ui/products/cart/CartScreenFigma.kt`

The CourseHeader now displays different UI based on status:

- **GO Status**: Green "Go" button that triggers course notification
- **PREPARING Status**: Orange "Preparing" text
- **COMPLETE Status**: Green "Complete" text

#### Visual Design
- **Go Button**: Green background (#2E7D32), white text, clickable
- **Preparing Text**: Orange color (#FF9800), medium weight font
- **Complete Text**: Green color (#4CAF50), medium weight font

## Functionality

### Go Button Click Flow
1. User clicks "Go" button on a course
2. Status immediately changes to "PREPARING"
3. `sendCoursesNotificationForCourse()` is called
4. API notification is sent for that specific course only
5. On success: Status remains "PREPARING"
6. On failure: Status reverts to "GO"

### Course-Specific Notifications
When the Go button is clicked:
- Only items assigned to that specific course are included in the notification
- API call includes: storeId, courseName, tableName, cartJson (for that course only)
- Other courses are unaffected

## API Integration

### Single Course Notification
```kotlin
// Example: Sending notification for "Starters" course only
productsScreenViewModel.sendCoursesNotificationForCourse("course_starters")
```

### API Request Example
```json
{
  "storeId": 158,
  "courseName": "Starters",
  "tableName": "T-12",
  "cartJson": "[{\"storeItem\":{\"id\":101,\"name\":\"Soup\"},\"quantity\":2,\"price\":8.50}]"
}
```

## Usage Examples

### Setting Course Status Programmatically
```kotlin
// Mark course as preparing
viewModel.updateCourseStatus("course_mains", CourseStatus.PREPARING)

// Mark course as complete
viewModel.markCourseAsComplete("course_desserts")

// Reset course status
viewModel.resetCourseStatus("course_starters")
```

### Getting Course Status
```kotlin
val status = viewModel.getCourseStatus("course_mains")
when (status) {
    CourseStatus.GO -> // Show Go button
    CourseStatus.PREPARING -> // Show Preparing text
    CourseStatus.COMPLETE -> // Show Complete text
}
```

## Benefits

### 1. Granular Control
- Send notifications for individual courses instead of all at once
- Track preparation status per course
- Better kitchen workflow management

### 2. Visual Feedback
- Clear visual indicators for each status
- Color-coded status display
- Intuitive button vs text distinction

### 3. Error Handling
- Status reverts on API failure
- Prevents duplicate notifications for preparing courses
- Clear status tracking

### 4. Flexibility
- Easy to extend with additional statuses
- Programmatic status control
- Reset functionality for corrections

## Future Enhancements

### Potential Additions
1. **Timer Integration**: Auto-complete after estimated preparation time
2. **Kitchen Feedback**: Allow kitchen to mark courses complete
3. **Status History**: Track status change timestamps
4. **Notifications**: Alert when courses are ready
5. **Batch Operations**: Mark multiple courses complete at once

### Status Persistence
Currently, course statuses are stored in memory. Future versions could:
- Persist statuses to local database
- Sync statuses across devices
- Maintain status history

## Testing

### Unit Tests
- `CourseStatusTest.kt` - Tests enum values and state management
- `SendCoursesNotificationUseCaseTest.kt` - Tests API integration

### Manual Testing Scenarios
1. Click Go button → Status changes to Preparing
2. API success → Status remains Preparing
3. API failure → Status reverts to Go
4. Multiple courses → Each course status independent
5. Course completion → Status changes to Complete

## Files Modified

### Core Implementation
- `CartScreenFigma.kt` - UI components and status display
- `ProductsScreenViewModel.kt` - State management and business logic

### Supporting Files
- `CourseStatusTest.kt` - Unit tests
- `COURSE_STATUS_IMPLEMENTATION.md` - Documentation

The implementation provides a robust, user-friendly system for managing course preparation status with clear visual feedback and reliable API integration.
