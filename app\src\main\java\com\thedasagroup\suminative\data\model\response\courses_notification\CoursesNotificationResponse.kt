package com.thedasagroup.suminative.data.model.response.courses_notification

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Response model for courses notification API
 * Endpoint: /api/printer/coursesNotification
 */
@Serializable
data class CoursesNotificationResponse(
    @SerialName("success")
    val success: Boolean = false,
    @SerialName("message")
    val message: String? = null,
    @SerialName("data")
    val data: String? = null
)

/**
 * Error response model for courses notification API
 * Used when the API returns an error response
 */
@Serializable
data class CoursesNotificationErrorResponse(
    @SerialName("timestamp")
    val timestamp: String? = null,
    @SerialName("status")
    val status: Int? = null,
    @SerialName("error")
    val error: String? = null,
    @SerialName("message")
    val message: String? = null,
    @SerialName("path")
    val path: String? = null
)
